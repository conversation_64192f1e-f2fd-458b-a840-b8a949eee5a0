package com.dzzz.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 多数据源配置类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
public class DataSourceConfig {

    /**
     * 正孚电子证照数据库数据源
     */
    @Bean(name = "zhengfuDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.zhengfu")
    public DataSource zhengfuDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 汇聚库数据源
     */
    @Bean(name = "huijuDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.huiju")
    public DataSource huijuDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 标准库数据源
     */
    @Bean(name = "biaozhunDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.biaozhun")
    public DataSource biaozhunDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 主题库数据源
     */
    @Bean(name = "zhutiDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.zhuti")
    public DataSource zhutiDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 正孚数据库SqlSessionFactory
     */
    @Bean(name = "zhengfuSqlSessionFactory")
    public SqlSessionFactory zhengfuSqlSessionFactory(@Qualifier("zhengfuDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/zhengfu/*.xml"));
        return bean.getObject();
    }

    /**
     * 汇聚库SqlSessionFactory
     */
    @Bean(name = "huijuSqlSessionFactory")
    public SqlSessionFactory huijuSqlSessionFactory(@Qualifier("huijuDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/huiju/*.xml"));
        return bean.getObject();
    }

    /**
     * 标准库SqlSessionFactory
     */
    @Bean(name = "biaozhunSqlSessionFactory")
    public SqlSessionFactory biaozhunSqlSessionFactory(@Qualifier("biaozhunDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/biaozhun/*.xml"));
        return bean.getObject();
    }

    /**
     * 主题库SqlSessionFactory
     */
    @Bean(name = "zhutiSqlSessionFactory")
    public SqlSessionFactory zhutiSqlSessionFactory(@Qualifier("zhutiDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/zhuti/*.xml"));
        return bean.getObject();
    }

    /**
     * 正孚数据库事务管理器
     */
    @Bean(name = "zhengfuTransactionManager")
    @Primary
    public DataSourceTransactionManager zhengfuTransactionManager(@Qualifier("zhengfuDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 汇聚库事务管理器
     */
    @Bean(name = "huijuTransactionManager")
    public DataSourceTransactionManager huijuTransactionManager(@Qualifier("huijuDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 标准库事务管理器
     */
    @Bean(name = "biaozhunTransactionManager")
    public DataSourceTransactionManager biaozhunTransactionManager(@Qualifier("biaozhunDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 主题库事务管理器
     */
    @Bean(name = "zhutiTransactionManager")
    public DataSourceTransactionManager zhutiTransactionManager(@Qualifier("zhutiDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 正孚数据库SqlSessionTemplate
     */
    @Bean(name = "zhengfuSqlSessionTemplate")
    public SqlSessionTemplate zhengfuSqlSessionTemplate(@Qualifier("zhengfuSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 汇聚库SqlSessionTemplate
     */
    @Bean(name = "huijuSqlSessionTemplate")
    public SqlSessionTemplate huijuSqlSessionTemplate(@Qualifier("huijuSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 标准库SqlSessionTemplate
     */
    @Bean(name = "biaozhunSqlSessionTemplate")
    public SqlSessionTemplate biaozhunSqlSessionTemplate(@Qualifier("biaozhunSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 主题库SqlSessionTemplate
     */
    @Bean(name = "zhutiSqlSessionTemplate")
    public SqlSessionTemplate zhutiSqlSessionTemplate(@Qualifier("zhutiSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 正孚数据库Mapper扫描配置
     */
    @Configuration
    @MapperScan(basePackages = "com.dzzz.mapper.zhengfu", sqlSessionFactoryRef = "zhengfuSqlSessionFactory")
    static class ZhengfuMapperScanConfig {
    }

    /**
     * 汇聚库Mapper扫描配置
     */
    @Configuration
    @MapperScan(basePackages = "com.dzzz.mapper.huiju", sqlSessionFactoryRef = "huijuSqlSessionFactory")
    static class HuijuMapperScanConfig {
    }

    /**
     * 标准库Mapper扫描配置
     */
    @Configuration
    @MapperScan(basePackages = "com.dzzz.mapper.biaozhun", sqlSessionFactoryRef = "biaozhunSqlSessionFactory")
    static class BiaozhunMapperScanConfig {
    }

    /**
     * 主题库Mapper扫描配置
     */
    @Configuration
    @MapperScan(basePackages = "com.dzzz.mapper.zhuti", sqlSessionFactoryRef = "zhutiSqlSessionFactory")
    static class ZhutiMapperScanConfig {
    }
} 