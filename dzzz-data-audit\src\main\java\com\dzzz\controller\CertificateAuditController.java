package com.dzzz.controller;

import com.dzzz.dto.AuditRequest;
import com.dzzz.dto.AuditResponse;
import com.dzzz.service.CertificateAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Enumeration;

/**
 * 证照稽核控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/certificate")
public class CertificateAuditController {

    @Autowired
    private CertificateAuditService certificateAuditService;

    // 默认的目录名称列表
    private static final String DEFAULT_CATALOG_NAMES = "'海上非自航船舶船员适任证书','内河船员培训许可证','海船普通船员适任证书','海船船员内河航线行驶资格证明','小型海船适任证书','中华人民共和国临时船舶国籍证书_中文','燃油污染损害民事责任保险或其他财务保证证书（国内）','海员外派机构资质证书','船上膳食服务辅助人员培训证明','船员培训质量管理体系证书','引航员船员适任证书','船上厨师培训合格证明','海船船员培训许可证','海上设施工作人员海上交通安全技能培训合格证明','内河船舶船员特殊培训合格证','中华人民共和国船舶国籍证书_中英文','中华人民共和国临时船舶国籍证书_中英文','海船船员健康证明','游艇驾驶证','特定航线江海直达船舶船员行驶资格证明培训合格证','燃油污染损害民事责任保险或其他财务保证证书（国际）','不参加航行和轮机值班海船船员适任证书','油污损害民事责任保险或其他财务保证证书（国内）','油污损害民事责任保险或其他财务保证证书（国际）','非持久性油类污染损害民事责任保险或其他财务保证证书','中华人民共和国船舶国籍证书_中文','海船高级船员适任证书','游艇驾驶证内河','残骸清除责任保险或其他财务保证证书','内河船舶船员适任证书','公务船船员适任证书','海船船员培训合格证书','海船船员适任证书承认签证','游艇驾驶证海上'";

    /**
     * 稽核证照数据（支持GET和POST，GET方式参数更简单）
     * 
     * @param idCard 身份证号
     * @param certificateId 证照ID
     * @param certificateNumber 证照编号
     * @param catalogName 目录名称
     * @param startTime 开始时间（与endTime同时传入）
     * @param endTime 结束时间（与startTime同时传入）
     * @param request HTTP请求对象
     * @return 稽核响应
     */
    @RequestMapping(value = "/audit", method = {RequestMethod.POST, RequestMethod.GET})
    public AuditResponse auditCertificate(
            @RequestParam(value = "idCard", required = false) String idCard,
            @RequestParam(value = "certificateId", required = false) String certificateId,
            @RequestParam(value = "certificateNumber", required = false) String certificateNumber,
            @RequestParam(value = "catalogName", required = false) String catalogName,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            HttpServletRequest request
    ) {
        // 添加详细的调试信息
        log.info("=== 请求调试信息 ===");
        log.info("请求URL: {}", request.getRequestURL());
        log.info("查询字符串: {}", request.getQueryString());
        log.info("请求方法: {}", request.getMethod());
        
        // 打印所有请求参数
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String paramValue = request.getParameter(paramName);
            log.info("参数 {} = {}", paramName, paramValue);
        }
        
        // 处理catalogName参数：如果不传则使用默认值
        if (catalogName == null || catalogName.trim().isEmpty()) {
            catalogName = DEFAULT_CATALOG_NAMES;
            log.info("catalogName参数未传入，使用默认值");
        }
        
        log.info("=== 参数解析结果 ===");
        log.info("收到证照稽核请求：idCard={}, certificateId={}, certificateNumber={}, catalogName={}, startTime={}, endTime={}", 
                idCard, certificateId, certificateNumber, catalogName, startTime, endTime);
        
        AuditRequest auditRequest = new AuditRequest();
        auditRequest.setIdCard(idCard);
        auditRequest.setCertificateId(certificateId);
        auditRequest.setCertificateNumber(certificateNumber);
        auditRequest.setCatalogName(catalogName);
        auditRequest.setStartTime(startTime);
        auditRequest.setEndTime(endTime);
        
        log.info("构建的AuditRequest对象: {}", auditRequest);
        
        return certificateAuditService.auditCertificate(auditRequest);
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public String health() {
        log.info("健康检查请求");
        return "电子证照数据稽核程序运行正常";
    }
} 