package com.dzzz.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 稽核请求DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class AuditRequest {

    /**
     * 用户身份证号
     */
    private String idCard;

    /**
     * 证照ID
     */
    private String certificateId;

    /**
     * 证照编号
     */
    private String certificateNumber;

    /**
     * 目录名称
     */
    private String catalogName;

    /**
     * 开始时间（格式：yyyyMMdd）
     */
    private String startTime;

    /**
     * 结束时间（格式：yyyyMMdd）
     */
    private String endTime;

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return (idCard != null && !idCard.trim().isEmpty()) ||
               (certificateId != null && !certificateId.trim().isEmpty()) ||
               (certificateNumber != null && !certificateNumber.trim().isEmpty()) ||
               (catalogName != null && !catalogName.trim().isEmpty() && 
                startTime != null && !startTime.trim().isEmpty() && 
                endTime != null && !endTime.trim().isEmpty());
    }
} 