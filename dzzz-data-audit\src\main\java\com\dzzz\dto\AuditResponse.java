package com.dzzz.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 稽核响应DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class AuditResponse {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应时间
     */
    private LocalDateTime responseTime;

    /**
     * 稽核结果
     */
    private List<AuditResult> results;

    /**
     * 稽核结果内部类
     */
    @Data
    public static class AuditResult {
        /**
         * 证照ID
         */
        private String certificateId;

        /**
         * 证照编号
         */
        private String certificateNumber;

        /**
         * 持证者名称
         */
        private String certificateHolder;

        /**
         * 证照类型
         */
        private String certificateType;

        /**
         * 稽核状态
         */
        private String auditStatus;

        /**
         * 稽核详情
         */
        private String auditDetails;

        /**
         * 是否已清洗
         */
        private Boolean isCleaned;

        /**
         * 是否已同步到主题库
         */
        private Boolean isSyncedToTheme;
    }

    /**
     * 成功响应
     */
    public static AuditResponse success(String message) {
        AuditResponse response = new AuditResponse();
        response.setCode("200");
        response.setMessage(message);
        response.setResponseTime(LocalDateTime.now());
        return response;
    }

    /**
     * 失败响应
     */
    public static AuditResponse error(String code, String message) {
        AuditResponse response = new AuditResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setResponseTime(LocalDateTime.now());
        return response;
    }
} 