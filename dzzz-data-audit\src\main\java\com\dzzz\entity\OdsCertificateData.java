package com.dzzz.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 汇聚库电子证照信息实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ods_certificate_data")
public class OdsCertificateData {

    /**
     * 证照数据主键
     */
    @TableId
    private String dataid;

    /**
     * 电子证照标识码
     */
    private String certificateid;

    /**
     * 目录ID
     */
    private String catalogid;

    /**
     * 目录名称
     */
    private String catalogname;

    /**
     * 模板ID
     */
    private String templateid;

    /**
     * 证照类型名称
     */
    private String certificatetype;

    /**
     * 证照类型代码
     */
    private String certificatetypecode;

    /**
     * 证照颁发机构
     */
    private String issuedept;

    /**
     * 证照颁发机构代码
     */
    private String issuedeptcode;

    /**
     * 证照所属地区编码
     */
    private String certificateareacode;

    /**
     * 持证者名称
     */
    private String certificateholder;

    /**
     * 持证者代码
     */
    private String certificateholdercode;

    /**
     * 持证者类型
     */
    private String certificateholdertype;

    /**
     * 证照编号
     */
    private String certificatenumber;

    /**
     * 颁证日期
     */
    private String issuedate;

    /**
     * 有效期起始日
     */
    private String validbegindate;

    /**
     * 有效期截止日
     */
    private String validenddate;

    /**
     * 照面拓展信息
     */
    private String surfacedata;

    /**
     * 证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
     */
    private String status;

    /**
     * 登记人员ID
     */
    private String creator;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * 最后操作人
     */
    private String operator;

    /**
     * 修改时间
     */
    private String updatetime;

    /**
     * 文件保存位置
     */
    private String filepath;

    /**
     * 同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
     */
    private String syncstatus;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 登记部门
     */
    private String deptid;

    /**
     * 申请编号
     */
    private String applynum;

    /**
     * 事项名称
     */
    private String affairname;

    /**
     * 事项类型
     */
    private String affairtype;

    /**
     * 服务对象
     */
    private String serverbusiness;

    /**
     * 事项ID
     */
    private String affairid;

    /**
     * 事项编号
     */
    private String affairnum;

    /**
     * 签章类型
     */
    private String qztype;

    /**
     * 证书类别
     */
    private String zztype;

    /**
     * 加签文件备用地址
     */
    private String drafturl;

    /**
     * 是否预览
     */
    private String isview;

    /**
     * 归档编号
     */
    private String sortname;

    /**
     * 备用字段
     */
    private String col1;

    /**
     * 验证日期
     */
    private String verifydate;

    /**
     * 验证
     */
    private String verification;

    /**
     * 统一信用代码
     */
    private String creditcode;

    /**
     * 印章名称
     */
    private String sealname;

    /**
     * 数据同步时间
     */
    private LocalDateTime fcdcDate;
} 