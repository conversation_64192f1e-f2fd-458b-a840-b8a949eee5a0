package com.dzzz.mapper.biaozhun;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dzzz.entity.DwdbCertificateData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标准库电子证照数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface DwdbCertificateDataMapper extends BaseMapper<DwdbCertificateData> {

    /**
     * 根据证照ID查询证照信息
     * 
     * @param certificateId 证照ID
     * @return 证照信息
     */
    DwdbCertificateData selectByCertificateId(@Param("certificateId") String certificateId);

    /**
     * 批量根据证照ID列表查询证照信息
     * 
     * @param certificateIds 证照ID列表
     * @return 证照信息列表
     */
    List<DwdbCertificateData> selectByCertificateIds(@Param("certificateIds") List<String> certificateIds);
} 