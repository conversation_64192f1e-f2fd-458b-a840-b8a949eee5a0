package com.dzzz.mapper.huiju;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dzzz.entity.OdsCertificateData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 汇聚库电子证照数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface OdsCertificateDataMapper extends BaseMapper<OdsCertificateData> {

    /**
     * 根据证照ID查询证照信息
     * 
     * @param certificateId 证照ID
     * @return 证照信息
     */
    OdsCertificateData selectByCertificateId(@Param("certificateId") String certificateId);

    /**
     * 批量根据证照ID列表查询证照信息
     * 
     * @param certificateIds 证照ID列表
     * @return 证照信息列表
     */
    List<OdsCertificateData> selectByCertificateIds(@Param("certificateIds") List<String> certificateIds);

    /**
     * 插入证照数据
     * 
     * @param certificateData 证照数据
     * @return 影响行数
     */
    int insertCertificateData(OdsCertificateData certificateData);
} 