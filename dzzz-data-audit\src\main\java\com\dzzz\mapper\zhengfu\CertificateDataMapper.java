package com.dzzz.mapper.zhengfu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dzzz.entity.CertificateData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 正孚电子证照数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface CertificateDataMapper extends BaseMapper<CertificateData> {

    /**
     * 根据身份证号查询证照信息
     * 
     * @param idCard 身份证号
     * @return 证照信息列表
     */
    List<CertificateData> selectByIdCard(@Param("idCard") String idCard);

    /**
     * 根据证照ID查询证照信息
     * 
     * @param certificateId 证照ID
     * @return 证照信息
     */
    CertificateData selectByCertificateId(@Param("certificateId") String certificateId);

    /**
     * 根据证照编号查询证照信息
     * 
     * @param certificateNumber 证照编号
     * @return 证照信息列表
     */
    List<CertificateData> selectByCertificateNumber(@Param("certificateNumber") String certificateNumber);

    /**
     * 根据目录名称和时间区间查询证照信息
     * 
     * @param catalogName 目录名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 证照信息列表
     */
    List<CertificateData> selectByCatalogNameAndCreateTime(@Param("catalogName") String catalogName, 
                                                          @Param("startTime") String startTime, 
                                                          @Param("endTime") String endTime, 
                                                          @Param("offset") int offset, 
                                                          @Param("limit") int limit);
} 