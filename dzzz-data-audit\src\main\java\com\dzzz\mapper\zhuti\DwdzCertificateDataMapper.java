package com.dzzz.mapper.zhuti;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dzzz.entity.DwdzCertificateData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 主题库电子证照数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface DwdzCertificateDataMapper extends BaseMapper<DwdzCertificateData> {

    /**
     * 根据证照ID查询证照信息
     * 
     * @param certificateId 证照ID
     * @return 证照信息
     */
    DwdzCertificateData selectByCertificateId(@Param("certificateId") String certificateId);

    /**
     * 批量根据证照ID列表查询证照信息
     * 
     * @param certificateIds 证照ID列表
     * @return 证照信息列表
     */
    List<DwdzCertificateData> selectByCertificateIds(@Param("certificateIds") List<String> certificateIds);

    /**
     * 插入证照数据
     * 
     * @param certificateData 证照数据
     * @return 影响行数
     */
    int insertCertificateData(DwdzCertificateData certificateData);
} 