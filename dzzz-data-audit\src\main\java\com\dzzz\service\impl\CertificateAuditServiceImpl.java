package com.dzzz.service.impl;

import com.dzzz.dto.AuditRequest;
import com.dzzz.dto.AuditResponse;
import com.dzzz.entity.CertificateData;
import com.dzzz.entity.DwdbCertificateData;
import com.dzzz.entity.DwdzCertificateData;
import com.dzzz.entity.OdsCertificateData;
import com.dzzz.mapper.biaozhun.DwdbCertificateDataMapper;
import com.dzzz.mapper.huiju.OdsCertificateDataMapper;
import com.dzzz.mapper.zhuti.DwdzCertificateDataMapper;
import com.dzzz.mapper.zhengfu.CertificateDataMapper;
import com.dzzz.service.CertificateAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 证照稽核服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class CertificateAuditServiceImpl implements CertificateAuditService {

    @Autowired
    private CertificateDataMapper zhengfuMapper;

    @Autowired
    private OdsCertificateDataMapper huijuMapper;

    @Autowired
    private DwdbCertificateDataMapper biaozhunMapper;

    @Autowired
    private DwdzCertificateDataMapper zhutiMapper;

    @Override
    public AuditResponse auditCertificate(AuditRequest request) {
        log.info("开始稽核证照数据，请求参数：{}", request);
        
        try {
            // 验证请求参数
            if (!request.isValid()) {
                log.error("稽核请求参数无效");
                return AuditResponse.error("400", "请求参数无效，请提供身份证号、证照ID或证照编号中的至少一个");
            }

            List<CertificateData> certificateList = new ArrayList<>();

            // 1. 根据传入参数查询正孚电子证照数据库
            if (request.getIdCard() != null && !request.getIdCard().trim().isEmpty()) {
                log.info("根据身份证号查询证照信息：{}", request.getIdCard());
                List<CertificateData> idCardResults = zhengfuMapper.selectByIdCard(request.getIdCard());
                certificateList.addAll(idCardResults);
                log.info("根据身份证号查询到 {} 条证照记录", idCardResults.size());
            }

            if (request.getCertificateId() != null && !request.getCertificateId().trim().isEmpty()) {
                log.info("根据证照ID查询证照信息：{}", request.getCertificateId());
                CertificateData certificateData = zhengfuMapper.selectByCertificateId(request.getCertificateId());
                if (certificateData != null) {
                    certificateList.add(certificateData);
                    log.info("根据证照ID查询到证照记录：{}", certificateData.getCertificateid());
                }
            }

            if (request.getCertificateNumber() != null && !request.getCertificateNumber().trim().isEmpty()) {
                log.info("根据证照编号查询证照信息：{}", request.getCertificateNumber());
                List<CertificateData> numberResults = zhengfuMapper.selectByCertificateNumber(request.getCertificateNumber());
                certificateList.addAll(numberResults);
                log.info("根据证照编号查询到 {} 条证照记录", numberResults.size());
            }

            // 新增：根据目录名称和时间区间分批查询
            if (request.getCatalogName() != null && !request.getCatalogName().trim().isEmpty()
                    && request.getStartTime() != null && !request.getStartTime().trim().isEmpty()
                    && request.getEndTime() != null && !request.getEndTime().trim().isEmpty()) {
                log.info("根据目录名称和时间区间查询证照信息：catalogName={}, startTime={}, endTime={}", 
                        request.getCatalogName(), request.getStartTime(), request.getEndTime());
                
                // 处理时间格式
                String startTimeStr = request.getStartTime() + " 00:00:00";
                String endTimeStr = request.getEndTime() + " 23:59:59";
                
                int offset = 0;
                int limit = 100000;
                while (true) {
                    List<CertificateData> batch = zhengfuMapper.selectByCatalogNameAndCreateTime(
                            request.getCatalogName(), startTimeStr, endTimeStr, offset, limit);
                    if (batch == null || batch.isEmpty()) break;
                    certificateList.addAll(batch);
                    log.info("本批次查询到 {} 条证照记录，offset={}", batch.size(), offset);
                    if (batch.size() < limit) break;
                    offset += limit;
                }
                log.info("根据目录名称和时间区间累计查询到 {} 条证照记录", certificateList.size());
                
                // 对于大批量数据，使用批量稽核优化
                if (certificateList.size() > 1000) {
                    log.info("检测到大批量数据（{}条），启用批量稽核优化", certificateList.size());
                    return auditCertificateBatch(certificateList);
                } else if (certificateList.size() > 100) {
                    log.info("检测到中等批量数据（{}条），启用简化稽核模式", certificateList.size());
                    return auditCertificateSimple(certificateList);
                }
            }

            if (certificateList.isEmpty()) {
                log.warn("未在正孚数据库中查询到相关证照信息");
                return AuditResponse.error("404", "未找到相关证照信息");
            }

            log.info("共查询到 {} 条证照记录，开始进行数据稽核", certificateList.size());

            List<AuditResponse.AuditResult> results = new ArrayList<>();

            // 2. 对每条证照记录进行稽核
            for (CertificateData certificate : certificateList) {
                AuditResponse.AuditResult result = auditSingleCertificate(certificate);
                results.add(result);
            }

            AuditResponse response = AuditResponse.success("证照数据稽核完成");
            response.setResults(results);

            log.info("证照数据稽核完成，共处理 {} 条记录", results.size());
            return response;

        } catch (Exception e) {
            log.error("证照数据稽核过程中发生异常", e);
            return AuditResponse.error("500", "稽核过程中发生异常：" + e.getMessage());
        }
    }

    /**
     * 稽核单个证照记录
     */
    private AuditResponse.AuditResult auditSingleCertificate(CertificateData certificate) {
        // 减少日志输出，提高性能
        // log.info("开始稽核证照：{}", certificate.getCertificateid());
        
        AuditResponse.AuditResult result = new AuditResponse.AuditResult();
        result.setCertificateId(certificate.getCertificateid());
        result.setCertificateNumber(certificate.getCertificatenumber());
        result.setCertificateHolder(certificate.getCertificateholder());
        result.setCertificateType(certificate.getCertificatetype());

        try {
            // 2.1 检查汇聚库是否已有数据
            OdsCertificateData existingOdsData = huijuMapper.selectByCertificateId(certificate.getCertificateid());
            
            if (existingOdsData != null) {
                // log.info("证照 {} 已存在于汇聚库中", certificate.getCertificateid());
                result.setAuditStatus("已清洗");
                result.setAuditDetails("证照已清洗，存在于汇聚库");
                result.setIsCleaned(true);
            } else {
                // log.info("证照 {} 不存在于汇聚库中，开始同步到汇聚库", certificate.getCertificateid());
                
                // 2.2 同步到汇聚库
                OdsCertificateData odsData = new OdsCertificateData();
                BeanUtils.copyProperties(certificate, odsData);
                odsData.setFcdcDate(LocalDateTime.now());
                
                int insertResult = huijuMapper.insertCertificateData(odsData);
                if (insertResult > 0) {
                    // log.info("证照 {} 成功同步到汇聚库", certificate.getCertificateid());
                    result.setAuditStatus("已同步到汇聚库");
                    result.setAuditDetails("证照已成功同步到汇聚库");
                    result.setIsCleaned(true);
                } else {
                    log.error("证照 {} 同步到汇聚库失败", certificate.getCertificateid());
                    result.setAuditStatus("同步失败");
                    result.setAuditDetails("证照同步到汇聚库失败");
                    result.setIsCleaned(false);
                    return result;
                }
            }

            // 3. 检查标准库是否有清洗好的数据
            DwdbCertificateData dwdbData = biaozhunMapper.selectByCertificateId(certificate.getCertificateid());
            
            if (dwdbData != null) {
                // log.info("证照 {} 在标准库中存在清洗好的数据", certificate.getCertificateid());
                
                // 3.1 检查主题库是否已有数据
                DwdzCertificateData existingDwdzData = zhutiMapper.selectByCertificateId(certificate.getCertificateid());
                
                if (existingDwdzData != null) {
                    // log.info("证照 {} 已存在于主题库中", certificate.getCertificateid());
                    result.setAuditStatus("已同步到主题库");
                    result.setAuditDetails("证照已清洗并同步到主题库");
                    result.setIsSyncedToTheme(true);
                } else {
                    // log.info("证照 {} 不存在于主题库中，开始同步到主题库", certificate.getCertificateid());
                    
                    // 3.2 同步到主题库
                    DwdzCertificateData dwdzData = new DwdzCertificateData();
                    BeanUtils.copyProperties(dwdbData, dwdzData);
                    dwdzData.setRecCreateDate(LocalDateTime.now());
                    dwdzData.setRecModifyDate(LocalDateTime.now());
                    
                    int syncResult = zhutiMapper.insertCertificateData(dwdzData);
                    if (syncResult > 0) {
                        // log.info("证照 {} 成功同步到主题库", certificate.getCertificateid());
                        result.setAuditStatus("已同步到主题库");
                        result.setAuditDetails("证照已清洗并成功同步到主题库");
                        result.setIsSyncedToTheme(true);
                    } else {
                        log.error("证照 {} 同步到主题库失败", certificate.getCertificateid());
                        result.setAuditStatus("主题库同步失败");
                        result.setAuditDetails("证照已清洗但同步到主题库失败");
                        result.setIsSyncedToTheme(false);
                    }
                }
            } else {
                // log.warn("证照 {} 在标准库中不存在清洗好的数据", certificate.getCertificateid());
                result.setAuditStatus("等待清洗");
                result.setAuditDetails("证照已同步到汇聚库，但标准库中还没有清洗好的数据");
                result.setIsSyncedToTheme(false);
            }

        } catch (Exception e) {
            log.error("稽核证照 {} 时发生异常", certificate.getCertificateid(), e);
            result.setAuditStatus("稽核异常");
            result.setAuditDetails("稽核过程中发生异常：" + e.getMessage());
            result.setIsCleaned(false);
            result.setIsSyncedToTheme(false);
        }

        // log.info("证照 {} 稽核完成，状态：{}", certificate.getCertificateid(), result.getAuditStatus());
        return result;
    }

    /**
     * 批量稽核证照记录（针对大批量数据优化）
     */
    private AuditResponse auditCertificateBatch(List<CertificateData> certificateList) {
        log.info("开始批量稽核 {} 条证照记录", certificateList.size());
        
        List<AuditResponse.AuditResult> results = new ArrayList<>();
        
        // 提取所有证照ID
        List<String> certificateIds = certificateList.stream()
                .map(CertificateData::getCertificateid)
                .collect(Collectors.toList());
        
        // 批量查询汇聚库中已存在的证照（优化：使用真正的批量查询）
        Set<String> existingOdsIds = new HashSet<>();
        try {
            int batchSize = 1000;
            int processedCount = 0;
            for (int i = 0; i < certificateIds.size(); i += batchSize) {
                int end = Math.min(i + batchSize, certificateIds.size());
                List<String> batchIds = certificateIds.subList(i, end);
                
                // 真正的批量查询
                List<OdsCertificateData> batchResults = huijuMapper.selectByCertificateIds(batchIds);
                for (OdsCertificateData odsData : batchResults) {
                    existingOdsIds.add(odsData.getCertificateid());
                }
                
                processedCount += batchIds.size();
                if (processedCount % 10000 == 0) {
                    log.info("汇聚库查询进度：{}/{}", processedCount, certificateIds.size());
                }
            }
            log.info("汇聚库中已存在 {} 条证照记录", existingOdsIds.size());
        } catch (Exception e) {
            log.error("批量查询汇聚库时发生异常", e);
        }
        
        // 批量查询标准库中已存在的证照（优化：使用真正的批量查询）
        Set<String> existingDwdbIds = new HashSet<>();
        try {
            int batchSize = 1000;
            int processedCount = 0;
            for (int i = 0; i < certificateIds.size(); i += batchSize) {
                int end = Math.min(i + batchSize, certificateIds.size());
                List<String> batchIds = certificateIds.subList(i, end);
                
                // 真正的批量查询
                List<DwdbCertificateData> batchResults = biaozhunMapper.selectByCertificateIds(batchIds);
                for (DwdbCertificateData dwdbData : batchResults) {
                    existingDwdbIds.add(dwdbData.getCertificateId());
                }
                
                processedCount += batchIds.size();
                if (processedCount % 10000 == 0) {
                    log.info("标准库查询进度：{}/{}", processedCount, certificateIds.size());
                }
            }
            log.info("标准库中已存在 {} 条证照记录", existingDwdbIds.size());
        } catch (Exception e) {
            log.error("批量查询标准库时发生异常", e);
        }
        
        // 批量查询主题库中已存在的证照（优化：使用真正的批量查询）
        Set<String> existingDwdzIds = new HashSet<>();
        try {
            int batchSize = 1000;
            int processedCount = 0;
            for (int i = 0; i < certificateIds.size(); i += batchSize) {
                int end = Math.min(i + batchSize, certificateIds.size());
                List<String> batchIds = certificateIds.subList(i, end);
                
                // 真正的批量查询
                List<DwdzCertificateData> batchResults = zhutiMapper.selectByCertificateIds(batchIds);
                for (DwdzCertificateData dwdzData : batchResults) {
                    existingDwdzIds.add(dwdzData.getCertificateId());
                }
                
                processedCount += batchIds.size();
                if (processedCount % 10000 == 0) {
                    log.info("主题库查询进度：{}/{}", processedCount, certificateIds.size());
                }
            }
            log.info("主题库中已存在 {} 条证照记录", existingDwdzIds.size());
        } catch (Exception e) {
            log.error("批量查询主题库时发生异常", e);
        }
        
        // 批量处理证照稽核
        List<OdsCertificateData> odsToInsert = new ArrayList<>();
        List<DwdzCertificateData> dwdzToInsert = new ArrayList<>();
        
        for (CertificateData certificate : certificateList) {
            String certId = certificate.getCertificateid();
            AuditResponse.AuditResult result = new AuditResponse.AuditResult();
            result.setCertificateId(certId);
            result.setCertificateNumber(certificate.getCertificatenumber());
            result.setCertificateHolder(certificate.getCertificateholder());
            result.setCertificateType(certificate.getCertificatetype());
            
            try {
                // 检查汇聚库
                if (existingOdsIds.contains(certId)) {
                    result.setAuditStatus("已清洗");
                    result.setAuditDetails("证照已清洗，存在于汇聚库");
                    result.setIsCleaned(true);
                } else {
                    // 准备同步到汇聚库
                    OdsCertificateData odsData = new OdsCertificateData();
                    BeanUtils.copyProperties(certificate, odsData);
                    odsData.setFcdcDate(LocalDateTime.now());
                    odsToInsert.add(odsData);
                    
                    result.setAuditStatus("待同步到汇聚库");
                    result.setAuditDetails("证照待同步到汇聚库");
                    result.setIsCleaned(false);
                }
                
                // 检查标准库和主题库
                if (existingDwdbIds.contains(certId)) {
                    if (existingDwdzIds.contains(certId)) {
                        result.setAuditStatus("已同步到主题库");
                        result.setAuditDetails("证照已清洗并同步到主题库");
                        result.setIsSyncedToTheme(true);
                    } else {
                        // 需要同步到主题库，但这里简化处理
                        result.setAuditStatus("待同步到主题库");
                        result.setAuditDetails("证照已清洗但待同步到主题库");
                        result.setIsSyncedToTheme(false);
                    }
                } else {
                    result.setAuditStatus("等待清洗");
                    result.setAuditDetails("证照已同步到汇聚库，但标准库中还没有清洗好的数据");
                    result.setIsSyncedToTheme(false);
                }
                
            } catch (Exception e) {
                log.error("稽核证照 {} 时发生异常", certId, e);
                result.setAuditStatus("稽核异常");
                result.setAuditDetails("稽核过程中发生异常：" + e.getMessage());
                result.setIsCleaned(false);
                result.setIsSyncedToTheme(false);
            }
            
            results.add(result);
        }
        
        // 批量插入汇聚库（优化：分批插入，避免单次事务过大）
        if (!odsToInsert.isEmpty()) {
            try {
                log.info("开始批量插入 {} 条记录到汇聚库", odsToInsert.size());
                int insertBatchSize = 500;
                int successCount = 0;
                
                for (int i = 0; i < odsToInsert.size(); i += insertBatchSize) {
                    int end = Math.min(i + insertBatchSize, odsToInsert.size());
                    List<OdsCertificateData> batch = odsToInsert.subList(i, end);
                    
                    for (OdsCertificateData odsData : batch) {
                        int insertResult = huijuMapper.insertCertificateData(odsData);
                        if (insertResult > 0) {
                            successCount++;
                        }
                    }
                    
                    if ((i + insertBatchSize) % 5000 == 0 || end == odsToInsert.size()) {
                        log.info("汇聚库插入进度：{}/{}", Math.min(i + insertBatchSize, odsToInsert.size()), odsToInsert.size());
                    }
                }
                
                log.info("成功批量插入 {} 条记录到汇聚库", successCount);
                
                // 更新结果状态
                for (AuditResponse.AuditResult result : results) {
                    if ("待同步到汇聚库".equals(result.getAuditStatus())) {
                        result.setAuditStatus("已同步到汇聚库");
                        result.setAuditDetails("证照已成功同步到汇聚库");
                        result.setIsCleaned(true);
                    }
                }
            } catch (Exception e) {
                log.error("批量插入汇聚库时发生异常", e);
            }
        }
        
        AuditResponse response = AuditResponse.success("批量证照数据稽核完成");
        response.setResults(results);
        
        log.info("批量证照数据稽核完成，共处理 {} 条记录", results.size());
        return response;
    }

    /**
     * 简化稽核证照记录（针对中等批量数据优化，减少日志输出）
     */
    private AuditResponse auditCertificateSimple(List<CertificateData> certificateList) {
        log.info("开始简化稽核 {} 条证照记录", certificateList.size());
        
        List<AuditResponse.AuditResult> results = new ArrayList<>();
        int processedCount = 0;
        
        for (CertificateData certificate : certificateList) {
            AuditResponse.AuditResult result = auditSingleCertificate(certificate);
            results.add(result);
            
            processedCount++;
            if (processedCount % 50 == 0) {
                log.info("简化稽核进度：{}/{}", processedCount, certificateList.size());
            }
        }
        
        AuditResponse response = AuditResponse.success("简化证照数据稽核完成");
        response.setResults(results);
        
        log.info("简化证照数据稽核完成，共处理 {} 条记录", results.size());
        return response;
    }
} 