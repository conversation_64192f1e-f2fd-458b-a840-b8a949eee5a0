server:
  port: 8027

spring:
  application:
    name: dzzz-data-audit
  
  # 多数据源配置
  datasource:
    # 正孚电子证照数据库配置
    zhengfu:
      jdbc-url: *******************************************
      username: system
      password: zE$HvDuK!e29h8s$dx
      driver-class-name: org.postgresql.Driver
      schema: testmsacert
      
    # 汇聚库数据库配置
    huiju:
      jdbc-url: *******************************************
      username: msa_dw
      password: 7#k9@QzR!2mXpL$5*B
      driver-class-name: org.postgresql.Driver
      schema: dzzz_ods
      
    # 标准库数据库配置
    biaozhun:
      jdbc-url: *******************************************
      username: msa_dw
      password: 7#k9@QzR!2mXpL$5*B
      driver-class-name: org.postgresql.Driver
      schema: msa_dwd
      
    # 主题库数据库配置
    zhuti:
      jdbc-url: *******************************************
      username: msa_dw
      password: 7#k9@QzR!2mXpL$5*B
      driver-class-name: org.postgresql.Driver
      schema: msa_dws

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      id-type: input
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.dzzz: INFO
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n" 