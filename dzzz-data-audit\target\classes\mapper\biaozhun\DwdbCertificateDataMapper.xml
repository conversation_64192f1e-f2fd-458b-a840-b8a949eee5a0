<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzzz.mapper.biaozhun.DwdbCertificateDataMapper">

    <!-- 根据证照ID查询证照信息 -->
    <select id="selectByCertificateId" resultType="com.dzzz.entity.DwdbCertificateData">
        SELECT *
        FROM dwdb_certificate_data
        WHERE certificate_id = #{certificateId}
        LIMIT 1
    </select>

    <!-- 批量根据证照ID列表查询证照信息 -->
    <select id="selectByCertificateIds" resultType="com.dzzz.entity.DwdbCertificateData">
        SELECT *
        FROM dwdb_certificate_data
        WHERE certificate_id IN
        <foreach collection="certificateIds" item="certificateId" open="(" separator="," close=")">
            #{certificateId}
        </foreach>
    </select>

</mapper> 