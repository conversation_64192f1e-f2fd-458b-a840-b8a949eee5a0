<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzzz.mapper.huiju.OdsCertificateDataMapper">

    <!-- 根据证照ID查询证照信息 -->
    <select id="selectByCertificateId" resultType="com.dzzz.entity.OdsCertificateData">
        SELECT *
        FROM ods_certificate_data
        WHERE certificateid = #{certificateId}
        LIMIT 1
    </select>

    <!-- 批量根据证照ID列表查询证照信息 -->
    <select id="selectByCertificateIds" resultType="com.dzzz.entity.OdsCertificateData">
        SELECT *
        FROM ods_certificate_data
        WHERE certificateid IN
        <foreach collection="certificateIds" item="certificateId" open="(" separator="," close=")">
            #{certificateId}
        </foreach>
    </select>

    <!-- 插入证照数据 -->
    <insert id="insertCertificateData" parameterType="com.dzzz.entity.OdsCertificateData">
        INSERT INTO ods_certificate_data (
            dataid, certificateid, catalogid, catalogname, templateid, certificatetype, certificatetypecode,
            issuedept, issuedeptcode, certificateareacode, certificateholder, certificateholdercode,
            certificateholdertype, certificatenumber, issuedate, validbegindate, validenddate, surfacedata,
            status, creator, createtime, operator, updatetime, filepath, syncstatus, remarks, deptid,
            applynum, affairname, affairtype, servebusiness, affairid, affairnum, qztype, zztype,
            drafturl, isview, sortname, col1, verifydate, verification, creditcode, sealname, fcdc_date
        ) VALUES (
            #{dataid}, #{certificateid}, #{catalogid}, #{catalogname}, #{templateid}, #{certificatetype}, #{certificatetypecode},
            #{issuedept}, #{issuedeptcode}, #{certificateareacode}, #{certificateholder}, #{certificateholdercode},
            #{certificateholdertype}, #{certificatenumber}, #{issuedate}, #{validbegindate}, #{validenddate}, #{surfacedata},
            #{status}, #{creator}, #{createtime}, #{operator}, #{updatetime}, #{filepath}, #{syncstatus}, #{remarks}, #{deptid},
            #{applynum}, #{affairname}, #{affairtype}, #{serverbusiness}, #{affairid}, #{affairnum}, #{qztype}, #{zztype},
            #{drafturl}, #{isview}, #{sortname}, #{col1}, #{verifydate}, #{verification}, #{creditcode}, #{sealname}, #{fcdcDate}
        )
    </insert>

</mapper> 