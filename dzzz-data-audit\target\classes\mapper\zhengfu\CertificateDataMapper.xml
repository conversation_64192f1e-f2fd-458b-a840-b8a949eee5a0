<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzzz.mapper.zhengfu.CertificateDataMapper">

    <!-- 根据身份证号查询证照信息 -->
    <select id="selectByIdCard" resultType="com.dzzz.entity.CertificateData">
        SELECT *
        FROM certificate_data
        WHERE certificateholdercode = #{idCard}
    </select>

    <!-- 根据证照ID查询证照信息 -->
    <select id="selectByCertificateId" resultType="com.dzzz.entity.CertificateData">
        SELECT *
        FROM certificate_data
        WHERE certificateid = #{certificateId}
    </select>

    <!-- 根据证照编号查询证照信息 -->
    <select id="selectByCertificateNumber" resultType="com.dzzz.entity.CertificateData">
        SELECT *
        FROM certificate_data
        WHERE certificatenumber = #{certificateNumber}
    </select>

    <!-- 根据目录名称和时间区间查询证照信息 -->
    <select id="selectByCatalogNameAndCreateTime" resultType="com.dzzz.entity.CertificateData">
        SELECT * FROM certificate_data
        WHERE status in ('0','1','2') 
          AND catalogname IN (${catalogName})
          <if test="startTime != null and startTime != ''">
            AND updatetime &gt;= #{startTime}
          </if>
          <if test="endTime != null and endTime != ''">
            AND updatetime &lt;= #{endTime}
          </if>
        LIMIT #{limit} OFFSET #{offset}
    </select>

</mapper> 