<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzzz.mapper.zhuti.DwdzCertificateDataMapper">

    <!-- 根据证照ID查询证照信息 -->
    <select id="selectByCertificateId" resultType="com.dzzz.entity.DwdzCertificateData">
        SELECT *
        FROM dwdz_certificate_data
        WHERE certificate_id = #{certificateId}
        LIMIT 1
    </select>

    <!-- 批量根据证照ID列表查询证照信息 -->
    <select id="selectByCertificateIds" resultType="com.dzzz.entity.DwdzCertificateData">
        SELECT *
        FROM dwdz_certificate_data
        WHERE certificate_id IN
        <foreach collection="certificateIds" item="certificateId" open="(" separator="," close=")">
            #{certificateId}
        </foreach>
    </select>

    <!-- 插入证照数据 -->
    <insert id="insertCertificateData" parameterType="com.dzzz.entity.DwdzCertificateData">
        INSERT INTO dwdz_certificate_data (
            data_id, certificate_id, template_id, certificate_type_name, certificate_type_code,
            certificate_define_authority_name, certificate_define_authority_code, related_item_name,
            related_item_code, certificate_holder_category, certificate_holder_category_name,
            validity_range, certificate_identifier, certificate_name, certificate_number,
            certificate_issuing_authority_name, certificate_issuing_authority_code, certificate_issued_date,
            certificate_holder_name, certificate_holder_code, certificate_holder_type_name,
            certificate_effective_date, certificate_expiring_date, issue_dept_code2, issue_dept_code3,
            certificate_area_code, surface_data, certificate_status, creator_id, create_time,
            operator_id, update_time, file_path, sync_status, remarks, dept_id, apply_num,
            affair_type, serve_business, affair_id, affair_num, qz_type, draft_url, sort_name,
            sealname, source_code, rec_create_date, rec_modify_date, msa_org_code, birth, name_en,
            country_cn, country_en, sign_dept_en, applivations_cn, crew_type, qualification_cn,
            birth_en, cert_print_no, certificate_effective_date_en, certificate_expiring_date_en,
            certificate_issued_date_en, crew_type_en, applivations_en, auth_authority_cn,
            auth_authority_en, eva_org_cn, eva_org_en, train_manager_name_cn, train_manager_name_en,
            representative_cn, representative_en, training_names_cn, training_names_en,
            training_issue_dates_cn, training_issue_dates_en, training_effective_dates_cn,
            training_effective_dates_en, training_institution_code, training_location
        ) VALUES (
            #{dataId}, #{certificateId}, #{templateId}, #{certificateTypeName}, #{certificateTypeCode},
            #{certificateDefineAuthorityName}, #{certificateDefineAuthorityCode}, #{relatedItemName},
            #{relatedItemCode}, #{certificateHolderCategory}, #{certificateHolderCategoryName},
            #{validityRange}, #{certificateIdentifier}, #{certificateName}, #{certificateNumber},
            #{certificateIssuingAuthorityName}, #{certificateIssuingAuthorityCode}, #{certificateIssuedDate},
            #{certificateHolderName}, #{certificateHolderCode}, #{certificateHolderTypeName},
            #{certificateEffectiveDate}, #{certificateExpiringDate}, #{issueDeptCode2}, #{issueDeptCode3},
            #{certificateAreaCode}, #{surfaceData}, #{certificateStatus}, #{creatorId}, #{createTime},
            #{operatorId}, #{updateTime}, #{filePath}, #{syncStatus}, #{remarks}, #{deptId}, #{applyNum},
            #{affairType}, #{serveBusiness}, #{affairId}, #{affairNum}, #{qzType}, #{draftUrl}, #{sortName},
            #{sealname}, #{sourceCode}, #{recCreateDate}, #{recModifyDate}, #{msaOrgCode}, #{birth}, #{nameEn},
            #{countryCn}, #{countryEn}, #{signDeptEn}, #{applivationsCn}, #{crewType}, #{qualificationCn},
            #{birthEn}, #{certPrintNo}, #{certificateEffectiveDateEn}, #{certificateExpiringDateEn},
            #{certificateIssuedDateEn}, #{crewTypeEn}, #{applivationsEn}, #{authAuthorityCn},
            #{authAuthorityEn}, #{evaOrgCn}, #{evaOrgEn}, #{trainManagerNameCn}, #{trainManagerNameEn},
            #{representativeCn}, #{representativeEn}, #{trainingNamesCn}, #{trainingNamesEn},
            #{trainingIssueDatesCn}, #{trainingIssueDatesEn}, #{trainingEffectiveDatesCn},
            #{trainingEffectiveDatesEn}, #{trainingInstitutionCode}, #{trainingLocation}
        )
    </insert>

</mapper> 