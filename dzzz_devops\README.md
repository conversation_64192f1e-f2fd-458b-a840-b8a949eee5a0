# 证书查询与校验工具

## 项目简介
证书查询与校验工具是一个命令行工具，用于查询证书信息、验证证书数据完整性以及下载证书文件。该工具主要针对船员证书系统，支持多种证书类型的查询和验证。

## 功能特点
- 根据证书编号查询证书基本信息
- 查看证书表面数据和请求数据的详细信息
- 验证证书数据的完整性，检查必填字段
- 下载证书文件和证书图片

## 支持的证书类型
系统目前支持以下证书类型的验证：

1. 内河船舶船员适任证书
2. 内河船舶船员培训合格证
3. 游艇驾驶证
4. 游艇驾驶证海上/游艇驾驶证（海上）
5. 游艇驾驶证内河/游艇驾驶证（内河）
6. 海船船员内河航线行驶资格证明
7. 海船船员培训合格证书
8. 海船船员健康证明
9. 海船普通船员适任证书
10. 不参加航行和轮机值班海船船员适任证书
11. 海船高级船员适任证书
12. 船上厨师培训合格证明
13. 小型海船适任证书
14. 海上非自航船舶船员适任证书
15. 公务船船员适任证书
16. 引航员船员适任证书
17. 特定航线江海直达船舶船员行驶资格证明培训合格证
18. 船上膳食服务辅助人员培训证明
19. 船员培训质量管理体系证书
20. 海上设施工作人员海上交通安全技能培训合格证明
21. 内河船员培训许可证
22. 海船船员培训许可证
23. 海员外派机构资质证书

##代码打包
mvn clean package

## 使用方法 
java -jar certificate-tool.jar <certificate_number> <isDetail> <isDownload>
参数说明：
  certificate_number: 证书编号，必填参数
  isDetail: 是否打印明细，0-不打印 1-打印，必填参数
  isDownload: 是否下载证书，0-不下载 1-下载，必填参数
示例：
  java -jar certificate-tool.jar FDA111202308554 1 0