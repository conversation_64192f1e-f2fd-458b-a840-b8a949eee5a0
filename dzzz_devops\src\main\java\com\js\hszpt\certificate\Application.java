package com.js.hszpt.certificate;

import com.js.hszpt.certificate.service.CertificateService;
import com.js.hszpt.certificate.util.CommandLineUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

/**
 * 证书查询与校验工具主类
 */
@SpringBootApplication
public class Application {
    
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
    
    @Bean
    public CommandLineRunner commandLineRunner(CertificateService certificateService) {
        return args -> {
            // 解析命令行参数
            if (!CommandLineUtil.validateArgs(args)) {
                // 参数验证失败，程序退出
                return;
            }
            
            String timeRange = args[0];  // 时间区间，格式：yyyyMMdd-yyyyMMdd
            boolean isDetail = "1".equals(args[1]);
            boolean isDownload = "1".equals(args[2]);
            
            // 执行证书查询和校验
            certificateService.processCertificatesByTimeRange(timeRange, isDetail, isDownload);
        };
    }
} 