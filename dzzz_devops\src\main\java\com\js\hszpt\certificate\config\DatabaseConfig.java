package com.js.hszpt.certificate.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库配置类 - 使用PostgreSQL驱动连接人大金仓数据库
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
public class DatabaseConfig {
    private String url;
    private String username;
    private String password;
    private String driverClassName;
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getDriverClassName() {
        return driverClassName;
    }
    
    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }
    
    /**
     * 获取数据库连接
     * @return 数据库连接对象
     * @throws SQLException 如果连接失败
     */
    public Connection getConnection() throws SQLException {
        try {
            // 加载数据库驱动
            Class.forName(driverClassName);
            System.out.println("数据库驱动加载成功: " + driverClassName);
            
            // 建立连接
            Connection conn = DriverManager.getConnection(url, username, password);
            System.out.println("数据库连接成功");
            return conn;
        } catch (ClassNotFoundException e) {
            System.out.println("加载数据库驱动失败: " + e.getMessage());
            throw new SQLException("加载数据库驱动失败", e);
        } catch (SQLException e) {
            System.out.println("数据库连接失败: " + e.getMessage());
            throw e;
        }
    }
} 