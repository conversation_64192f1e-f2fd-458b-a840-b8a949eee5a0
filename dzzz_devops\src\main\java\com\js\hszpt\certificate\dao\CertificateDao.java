package com.js.hszpt.certificate.dao;

import com.js.hszpt.certificate.config.DatabaseConfig;
import com.js.hszpt.certificate.model.Certificate;
import com.js.hszpt.certificate.model.CertificateLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

/**
 * 证书数据访问对象
 */
@Repository
public class CertificateDao {
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    /**
     * 根据证书编号查询证书信息
     * @param certificateNumber 证书编号
     * @return 证书对象，如果未找到则返回null
     */
    public Certificate findCertificateByNumber(String certificateNumber) {
        String sql = "SELECT data_id, certificate_number, certificate_id, catalog_name, surface_data " +
                     "FROM ctf_certificate_data_regen " +
                     "WHERE certificate_number = ?";
        
        try (Connection conn = databaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, certificateNumber);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Certificate certificate = new Certificate();
                    certificate.setDataId(rs.getString("data_id"));
                    certificate.setCertificateNumber(rs.getString("certificate_number"));
                    certificate.setCertificateId(rs.getString("certificate_id"));
                    certificate.setCatalogName(rs.getString("catalog_name"));
                    certificate.setSurfaceData(rs.getString("surface_data"));
                    return certificate;
                }
            }
        } catch (SQLException e) {
            System.out.println("查询证书信息失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 根据数据ID查询证书日志信息
     * @param dataId 数据ID
     * @return 证书日志对象，如果未找到则返回null
     */
    public CertificateLog findCertificateLogByDataId(String dataId) {
        String sql = "SELECT data_id, certificate_id, catalog_name, request_data, response_data " +
                     "FROM ctf_certificate_data_regen_log " +
                     "WHERE data_id = ?";
        
        try (Connection conn = databaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, dataId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    CertificateLog log = new CertificateLog();
                    log.setDataId(rs.getString("data_id"));
                    log.setCertificateId(rs.getString("certificate_id"));
                    log.setCatalogName(rs.getString("catalog_name"));
                    log.setRequestData(rs.getString("request_data"));
                    log.setResponseData(rs.getString("response_data"));
                    return log;
                }
            }
        } catch (SQLException e) {
            System.out.println("查询证书日志信息失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 根据时间区间查询证书信息
     * @param startTime 开始时间（格式：yyyyMMdd）
     * @param endTime 结束时间（格式：yyyyMMdd）
     * @return 证书对象列表
     */
    public List<Certificate> findCertificatesByTimeRange(String startTime, String endTime) {
        String sql = "SELECT data_id, certificate_number, certificate_id, catalog_name, surface_data, regen_time " +
                     "FROM ctf_certificate_data_regen " +
                     "WHERE regen_time BETWEEN ? AND ?";
        
        List<Certificate> certificates = new ArrayList<>();
        
        try (Connection conn = databaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            // 将yyyyMMdd格式的字符串转换为yyyy-MM-dd HH:mm:ss格式，避免Timestamp.valueOf报错
            String start = startTime.substring(0,4) + "-" + startTime.substring(4,6) + "-" + startTime.substring(6,8) + " 00:00:00";
            String end = endTime.substring(0,4) + "-" + endTime.substring(4,6) + "-" + endTime.substring(6,8) + " 23:59:59";
            stmt.setTimestamp(1, Timestamp.valueOf(start));
            stmt.setTimestamp(2, Timestamp.valueOf(end));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Certificate certificate = new Certificate();
                    certificate.setDataId(rs.getString("data_id"));
                    certificate.setCertificateNumber(rs.getString("certificate_number"));
                    certificate.setCertificateId(rs.getString("certificate_id"));
                    certificate.setCatalogName(rs.getString("catalog_name"));
                    certificate.setSurfaceData(rs.getString("surface_data"));
                    certificate.setRegenTime(rs.getTimestamp("regen_time"));
                    certificates.add(certificate);
                }
            }
        } catch (SQLException e) {
            System.out.println("查询证书信息失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return certificates;
    }
} 