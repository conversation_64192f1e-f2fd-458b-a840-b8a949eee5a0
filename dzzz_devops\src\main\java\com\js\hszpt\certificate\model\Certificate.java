package com.js.hszpt.certificate.model;

import java.util.Date;

/**
 * 证书数据模型
 */
public class Certificate {
    private String dataId;
    private String certificateNumber;
    private String certificateId;
    private String catalogName;
    private String surfaceData;
    private Date regenTime;  // 处理时间
    
    // 构造函数
    public Certificate() {}
    
    public Certificate(String dataId, String certificateNumber, String certificateId, 
                      String catalogName, String surfaceData, Date regenTime) {
        this.dataId = dataId;
        this.certificateNumber = certificateNumber;
        this.certificateId = certificateId;
        this.catalogName = catalogName;
        this.surfaceData = surfaceData;
        this.regenTime = regenTime;
    }
    
    // Getter 和 Setter 方法
    public String getDataId() {
        return dataId;
    }
    
    public void setDataId(String dataId) {
        this.dataId = dataId;
    }
    
    public String getCertificateNumber() {
        return certificateNumber;
    }
    
    public void setCertificateNumber(String certificateNumber) {
        this.certificateNumber = certificateNumber;
    }
    
    public String getCertificateId() {
        return certificateId;
    }
    
    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }
    
    public String getCatalogName() {
        return catalogName;
    }
    
    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }
    
    public String getSurfaceData() {
        return surfaceData;
    }
    
    public void setSurfaceData(String surfaceData) {
        this.surfaceData = surfaceData;
    }
    
    public Date getRegenTime() {
        return regenTime;
    }
    
    public void setRegenTime(Date regenTime) {
        this.regenTime = regenTime;
    }
} 