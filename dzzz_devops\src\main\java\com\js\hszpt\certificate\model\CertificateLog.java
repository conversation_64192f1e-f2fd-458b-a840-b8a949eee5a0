package com.js.hszpt.certificate.model;

/**
 * 证书日志数据模型
 */
public class CertificateLog {
    private String dataId;
    private String certificateId;
    private String catalogName;
    private String requestData;
    private String responseData;
    
    // 构造函数
    public CertificateLog() {}
    
    public CertificateLog(String dataId, String certificateId, String catalogName, 
                         String requestData, String responseData) {
        this.dataId = dataId;
        this.certificateId = certificateId;
        this.catalogName = catalogName;
        this.requestData = requestData;
        this.responseData = responseData;
    }
    
    // Getter 和 Setter 方法
    public String getDataId() {
        return dataId;
    }
    
    public void setDataId(String dataId) {
        this.dataId = dataId;
    }
    
    public String getCertificateId() {
        return certificateId;
    }
    
    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }
    
    public String getCatalogName() {
        return catalogName;
    }
    
    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }
    
    public String getRequestData() {
        return requestData;
    }
    
    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }
    
    public String getResponseData() {
        return responseData;
    }
    
    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }
} 