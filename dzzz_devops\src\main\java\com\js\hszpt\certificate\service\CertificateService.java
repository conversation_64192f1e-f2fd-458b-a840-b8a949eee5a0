package com.js.hszpt.certificate.service;

import com.js.hszpt.certificate.dao.CertificateDao;
import com.js.hszpt.certificate.model.Certificate;
import com.js.hszpt.certificate.model.CertificateLog;
import com.js.hszpt.certificate.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 证书服务类
 */
@Service
public class CertificateService {
    
    @Autowired
    private CertificateDao certificateDao;
    
    @Autowired
    private DownloadService downloadService;
    
    @Autowired
    private ValidationService validationService;
    
    /**
     * 处理证书查询和校验
     * @param certificateNumber 证书编号
     * @param isDetail 是否打印详情
     * @param isDownload 是否下载证书
     */
    public void processCertificate(String certificateNumber, boolean isDetail, boolean isDownload) {
        System.out.println("开始处理证书: " + certificateNumber);
        
        // 查询证书信息
        Certificate certificate = certificateDao.findCertificateByNumber(certificateNumber);
        if (certificate == null) {
            System.out.println("未找到证书编号为 " + certificateNumber + " 的证书");
            return;
        }
        
        // 打印证书基本信息
        printCertificateInfo(certificate);
        
        // 如果需要打印详情
        if (isDetail) {
            // 打印证书表面数据
            printSurfaceData(certificate.getSurfaceData());
            
            // 查询证书日志
            CertificateLog log = certificateDao.findCertificateLogByDataId(certificate.getDataId());
            if (log != null) {
                // 打印请求数据
                printRequestData(log.getRequestData());
                
                // 打印响应数据
                printResponseData(log.getResponseData());
                
                // 验证证书数据 - 传递表面数据
                validationService.validateCertificateData(certificate.getCatalogName(), log.getRequestData(), certificate.getSurfaceData());
            } else {
                System.out.println("未找到证书日志数据");
            }
        }
        
        // 如果需要下载证书
        if (isDownload) {
            downloadService.downloadCertificateFiles(certificate.getCertificateId());
        }
        
        System.out.println("\n证书处理完成");
    }
    
    /**
     * 处理时间区间内的证书查询和校验
     * @param timeRange 时间区间（格式：yyyyMMdd-yyyyMMdd）
     * @param isDetail 是否打印详情
     * @param isDownload 是否下载证书
     */
    public void processCertificatesByTimeRange(String timeRange, boolean isDetail, boolean isDownload) {
        // 解析时间区间
        String[] times = timeRange.split("-");
        if (times.length != 2) {
            System.out.println("时间区间格式错误，正确格式为：yyyyMMdd-yyyyMMdd");
            return;
        }
        
        String startTime = times[0];
        String endTime = times[1];
        
        System.out.println("开始处理时间区间内的证书: " + startTime + " 至 " + endTime);
        
        // 查询时间区间内的证书
        List<Certificate> certificates = certificateDao.findCertificatesByTimeRange(startTime, endTime);
        if (certificates.isEmpty()) {
            System.out.println("未找到该时间区间内的证书");
            return;
        }
        
        System.out.println("找到 " + certificates.size() + " 个证书");
        
        // 处理每个证书
        for (Certificate certificate : certificates) {
            System.out.println("\n处理证书: " + certificate.getCertificateNumber());
            
            // 打印证书基本信息
            printCertificateInfo(certificate);
            
            // 如果需要打印详情
            if (isDetail) {
                // 打印证书表面数据
//                printSurfaceData(certificate.getSurfaceData());
                
                // 查询证书日志
                CertificateLog log = certificateDao.findCertificateLogByDataId(certificate.getDataId());
                if (log != null) {
                    // 打印请求数据
//                    printRequestData(log.getRequestData());
                    
                    // 打印响应数据
//                    printResponseData(log.getResponseData());
                    
                    // 验证证书数据
                    validationService.validateCertificateData(certificate.getCatalogName(), log.getRequestData(), certificate.getSurfaceData());
                } else {
                    System.out.println("未找到证书日志数据");
                }
            }
            
            // 如果需要下载证书
            if (isDownload) {
                downloadService.downloadCertificateFiles(certificate.getCertificateId());
            }
        }
        
        System.out.println("\n时间区间内证书处理完成");
    }
    
    /**
     * 打印证书基本信息
     * @param certificate 证书对象
     */
    private void printCertificateInfo(Certificate certificate) {
        System.out.println("\n===== 证书基本信息 =====");
        System.out.println("数据ID: " + certificate.getDataId());
        System.out.println("证书编号: " + certificate.getCertificateNumber());
        System.out.println("证书ID: " + certificate.getCertificateId());
        System.out.println("证书类型: " + certificate.getCatalogName());
    }
    
    /**
     * 打印证书表面数据
     * @param surfaceData 表面数据JSON
     */
    private void printSurfaceData(String surfaceData) {
        System.out.println("\n===== 证书表面数据 =====");
        String formattedJson = JsonUtil.formatJson(surfaceData);
        System.out.println(formattedJson);
    }
    
    /**
     * 打印请求数据
     * @param requestData 请求数据JSON
     */
    private void printRequestData(String requestData) {
        System.out.println("\n===== 证书请求数据 =====");
        String formattedJson = JsonUtil.formatJson(requestData);
        System.out.println(formattedJson);
    }
    
    /**
     * 打印响应数据
     * @param responseData 响应数据JSON
     */
    private void printResponseData(String responseData) {
        System.out.println("\n===== 证书响应数据 =====");
        String formattedJson = JsonUtil.formatJson(responseData);
        System.out.println(formattedJson);
    }
} 