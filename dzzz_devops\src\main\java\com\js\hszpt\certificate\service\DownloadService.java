package com.js.hszpt.certificate.service;

import com.js.hszpt.certificate.util.HttpUtil;
import org.springframework.stereotype.Service;

/**
 * 证书下载服务类
 */
@Service
public class DownloadService {
    
    /**
     * 下载证书文件和图片
     * @param certificateId 证书ID
     */
    public void downloadCertificateFiles(String certificateId) {
        System.out.println("\n===== 开始下载证书文件 =====");
        
        // 下载证书详情文件
        boolean detailSuccess = HttpUtil.downloadCertificate(certificateId);
        if (detailSuccess) {
            System.out.println("证书详情文件下载成功");
        } else {
            System.out.println("证书详情文件下载失败");
        }
        
        // 下载证书图片
        boolean imageSuccess = HttpUtil.downloadCertificateImage(certificateId);
        if (imageSuccess) {
            System.out.println("证书图片下载成功");
        } else {
            System.out.println("证书图片下载失败");
        }
        
        System.out.println("===== 证书文件下载完成 =====");
    }
} 