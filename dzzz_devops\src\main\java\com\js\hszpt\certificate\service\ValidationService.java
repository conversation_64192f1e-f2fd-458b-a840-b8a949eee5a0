package com.js.hszpt.certificate.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.js.hszpt.certificate.util.JsonUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 证书验证服务类
 */
@Service
public class ValidationService {
    
    /**
     * 验证证书数据
     * @param catalogName 证书类型名称
     * @param requestData 请求数据JSON
     * @param surfaceData 表面数据JSON
     */
    public void validateCertificateData(String catalogName, String requestData, String surfaceData) {
        System.out.println("\n===== 证书数据验证 =====");
        System.out.println("证书类型: " + catalogName);
        
        // 检查空值字段
        List<String> emptyFields = JsonUtil.getEmptyValueFields(requestData);
        if (!emptyFields.isEmpty()) {
            System.out.println("\n以下字段值为空:");
            for (String field : emptyFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有字段都有值");
        }
        
        // 根据证书类型进行特定验证
        if ("内河船舶船员适任证书".equals(catalogName)) {
            validateInlandCrewCertificate(requestData);
        } else if ("内河船舶船员培训合格证".equals(catalogName)) {
            validateInlandCrewTrainingCertificate(requestData);
        } else if ("游艇驾驶证".equals(catalogName)) {
            validateYachtLicense(requestData, surfaceData);
        } else if ("游艇驾驶证海上".equals(catalogName) || "游艇驾驶证（海上）".equals(catalogName)) {
            validateMaritimeYachtLicense(requestData);
        } else if ("游艇驾驶证内河".equals(catalogName) || "游艇驾驶证（内河）".equals(catalogName)) {
            validateInlandYachtLicense(requestData);
        } else if ("海船船员内河航线行驶资格证明".equals(catalogName)) {
            validateSeaCrewInlandQualification(requestData);
        } else if ("海船船员培训合格证书".equals(catalogName)) {
            validateSeaCrewTrainingCertificate(requestData);
        } else if ("海船船员健康证明".equals(catalogName)) {
            validateSeaCrewHealthCertificate(requestData);
        } else if ("海船普通船员适任证书".equals(catalogName)) {
            validateSeaCrewCompetencyCertificate(requestData);
        } else if ("不参加航行和轮机值班海船船员适任证书".equals(catalogName)) {
            validateNonWatchkeepingSeaCrewCertificate(requestData);
        } else if ("海船高级船员适任证书".equals(catalogName)) {
            validateSeniorSeaCrewCertificate(requestData);
        } else if ("船上厨师培训合格证明".equals(catalogName)) {
            validateShipCookCertificate(requestData);
        } else if ("小型海船适任证书".equals(catalogName)) {
            validateSmallSeaVesselCertificate(requestData);
        } else if ("海上非自航船舶船员适任证书".equals(catalogName)) {
            validateNonSelfPropelledVesselCertificate(requestData);
        } else if ("公务船船员适任证书".equals(catalogName)) {
            validateOfficialVesselCertificate(requestData);
        } else if ("引航员船员适任证书".equals(catalogName)) {
            validatePilotCertificate(requestData);
        } else if ("特定航线江海直达船舶船员行驶资格证明培训合格证".equals(catalogName)) {
            validateSpecificRouteTrainingCertificate(requestData);
        } else if ("船上膳食服务辅助人员培训证明".equals(catalogName)) {
            validateShipMealServiceAssistantCertificate(requestData);
        } else if ("船员培训质量管理体系证书".equals(catalogName)) {
            validateCrewTrainingQualityManagementCertificate(requestData);
        } else if ("海上设施工作人员海上交通安全技能培训合格证明".equals(catalogName)) {
            validateMaritimeFacilityWorkerSafetyCertificate(requestData);
        } else if ("内河船员培训许可证".equals(catalogName)) {
            validateInlandCrewTrainingPermit(requestData);
        } else if ("海船船员培训许可证".equals(catalogName)) {
            validateSeaCrewTrainingPermit(requestData);
        } else if ("海员外派机构资质证书".equals(catalogName)) {
            validateSeafarerDispatchAgencyQualificationCertificate(requestData);
        }
    }
    
    /**
     * 验证海员外派机构资质证书
     * @param requestData 请求数据JSON
     */
    private void validateSeafarerDispatchAgencyQualificationCertificate(String requestData) {
        System.out.println("\n===== 海员外派机构资质证书验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "permitNumber1", "anThorityName1", "anThorityName2", "address1", "address2", 
            "representative1", "representative2", "serviceInclude1", "serviceInclude2", 
            "expiryDate1", "remark1", "remark2", "issuingAuthority1", "issuingAuthority2", 
            "dateofIssue1", "permitNumber2", "anThorityName3", "anThorityName4", 
            "address3", "address4", "representative3", "representative4", 
            "serviceInclude3", "serviceInclude4", "expiryDate2", "annualExamination", 
            "issuingAuthority3", "issuingAuthority4", "dateofIssue3"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证海船船员培训许可证
     * @param requestData 请求数据JSON
     */
    private void validateSeaCrewTrainingPermit(String requestData) {
        System.out.println("\n===== 海船船员培训许可证验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "permitNumber1", "anThorityName1", "trainingInstitutionCode1", "representative1", 
            "trainingProgram1", "trainingProgram2", "registeredAddress1", "trainingLocation1", 
            "periodOfValidity1", "periodOfValidity2", "issuingAuthority1", "dateofIssue1", 
            "anThorityName2", "permitNumber2", "registeredAddress2", "representative2", 
            "trainingLocation2", "periodOfValidity3", "periodOfValidity4", "remarks", 
            "number1", "number2", "number3", "number4", "number5", "number6", "number7", 
            "number8", "number9", "number10", "number11", 
            "atrainingProgram1", "atrainingProgram2", "atrainingProgram3", "atrainingProgram4", 
            "atrainingProgram5", "atrainingProgram6", "atrainingProgram7", "atrainingProgram8", 
            "atrainingProgram9", "atrainingProgram10", "atrainingProgram11", 
            "trainingScale1", "trainingScale2", "trainingScale3", "trainingScale4", 
            "trainingScale5", "trainingScale6", "trainingScale7", "trainingScale8", 
            "trainingScale9", "trainingScale10", "trainingScale11", 
            "issuingAuthority2", "dateofIssue2"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证内河船舶船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validateInlandCrewCertificate(String requestData) {
        System.out.println("\n===== 内河船舶船员适任证书验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "name", "sex", "number", "type", "endDate", 
            "signDept", "printNo", "scope", "photo", "issueDept", "signDate"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证内河船舶船员培训合格证
     * @param requestData 请求数据JSON
     */
    private void validateInlandCrewTrainingCertificate(String requestData) {
        System.out.println("\n===== 内河船舶船员培训合格证验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "name", "sex", "number", "printNo", "photo"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 动态检查记录字段组
        validateTrainingRecords(requestData);
    }
    
    /**
     * 验证培训记录字段
     * @param requestData 请求数据JSON
     */
    private void validateTrainingRecords(String requestData) {
        System.out.println("\n===== 培训记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"project数字"格式的字段
                    if (name.matches("project\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(7);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何培训记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条培训记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "project" + recordNumber,
                    "signDept" + recordNumber,
                    "signDate" + recordNumber,
                    "endDate" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证培训记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证游艇驾驶证
     * @param requestData 请求数据JSON
     * @param surfaceData 表面数据JSON
     */
    private void validateYachtLicense(String requestData, String surfaceData) {
        System.out.println("\n===== 游艇驾驶证验证 =====");
        
        // 获取qualificationCn的值，判断是海上证书还是内河证书
        String qualificationCn = getQualificationCnValue(surfaceData);
        
        if (qualificationCn != null && qualificationCn.startsWith("A")) {
            // 海上证书
            validateMaritimeYachtLicense(requestData);
        } else if (qualificationCn != null && qualificationCn.startsWith("B")) {
            // 内河证书
            validateInlandYachtLicense(requestData);
        } else {
            System.out.println("无法确定游艇驾驶证类型，qualificationCn值: " + qualificationCn);
        }
        
        // 验证记录字段
        validateYachtLicenseRecords(requestData);
    }
    
    /**
     * 获取表面数据中qualificationCn的值
     * @param surfaceData 表面数据JSON
     * @return qualificationCn的值，如果不存在则返回null
     */
    private String getQualificationCnValue(String surfaceData) {
        try {
            JsonNode rootNode = JsonUtil.parseJson(surfaceData);
            if (rootNode != null) {
                if (rootNode.isArray()) {
                    // 如果是数组格式
                    for (JsonNode item : rootNode) {
                        if (item.has("name") && "qualificationCn".equals(item.get("name").asText()) && item.has("value")) {
                            return item.get("value").asText();
                        }
                    }
                } else if (rootNode.has("surface") && rootNode.get("surface").isArray()) {
                    // 如果是包含surface数组的格式
                    JsonNode surfaceArray = rootNode.get("surface");
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && "qualificationCn".equals(item.get("name").asText()) && item.has("value")) {
                            return item.get("value").asText();
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("解析表面数据获取qualificationCn值失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 验证海上游艇驾驶证
     * @param requestData 请求数据JSON
     */
    private void validateMaritimeYachtLicense(String requestData) {
        System.out.println("\n===== 游艇驾驶证海上证书验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "fullNameOfTheHolder1", "fullNameOfTheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "dateOfExpiry1", "dateOfExpiry2", "issuedOn1", "issuedOn2",
            "issuingAdministration1", "issuingAdministration2", "photo"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证内河游艇驾驶证
     * @param requestData 请求数据JSON
     */
    private void validateInlandYachtLicense(String requestData) {
        System.out.println("\n===== 游艇驾驶证内河证书验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "name", "nationality", "sex", "dateOfBirth", "number",
            "dateOfExpiry", "initialDate", "officeOfissue", "photo"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证游艇驾驶证记录字段
     * @param requestData 请求数据JSON
     */
    private void validateYachtLicenseRecords(String requestData) {
        System.out.println("\n===== 游艇驾驶证记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"category数字"格式的字段
                    if (name.matches("category\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "category" + recordNumber,
                    "class" + recordNumber,
                    "propulsionType" + recordNumber,
                    "limitation" + recordNumber  // 注意：需求中是linination，但这可能是拼写错误，应该是limitation
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证游艇驾驶证记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证海船船员内河航线行驶资格证明
     * @param requestData 请求数据JSON
     */
    private void validateSeaCrewInlandQualification(String requestData) {
        System.out.println("\n===== 海船船员内河航线行驶资格证明验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "name", "gender", "creditCode", "numberOfCertificate", 
            "dateOfIssue", "applivations", "issuingAuthority", 
            "issuingDate", "photo"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证海船船员培训合格证书
     * @param requestData 请求数据JSON
     */
    private void validateSeaCrewTrainingCertificate(String requestData) {
        System.out.println("\n===== 海船船员培训合格证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "issuedOn1", "issuedOn2", "official1", "official2", "nameOfDulyAutOffi1", 
            "nameOfDulyAutOffi2", "officalSeal1", "officalSeal2", "officialUseOnly1", 
            "officialUseOnly2", "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证培训记录
        validateSeaCrewTrainingRecords(requestData);
    }
    
    /**
     * 验证海船船员培训合格证书记录
     * @param requestData 请求数据JSON
     */
    private void validateSeaCrewTrainingRecords(String requestData) {
        System.out.println("\n===== 海船船员培训记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过prefix字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"prefix数字"格式的字段
                    if (name.matches("prefix\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(6);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何培训记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条培训记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                // 计算中英文字段的索引
                int chineseIndex = recordNumber * 2 - 1;  // 奇数索引
                int englishIndex = recordNumber * 2;      // 偶数索引
                
                // 构建记录字段列表
                List<String> recordFields = new ArrayList<>();
                recordFields.add("prefix" + recordNumber);
                recordFields.add("titleOftheCertificate" + chineseIndex);
                recordFields.add("titleOftheCertificate" + englishIndex);
                recordFields.add("level" + recordNumber);
                recordFields.add("dateOfIssue" + chineseIndex);
                recordFields.add("dateOfIssue" + englishIndex);
                recordFields.add("dateOfExpiry" + chineseIndex);
                recordFields.add("dateOfExpiry" + englishIndex);
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证海船船员培训记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证海船船员健康证明
     * @param requestData 请求数据JSON
     */
    private void validateSeaCrewHealthCertificate(String requestData) {
        System.out.println("\n===== 海船船员健康证明验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "department1", "department2",
            "certificateNo", "certificateExpiringDate1", "certificateExpiringDate2",
            "dateOfIssue1", "dateOfIssue2", "yesOrNo1", "yesOrNo2", "yesOrNo3", "yesOrNo4",
            "yesOrNo5", "yesOrNo6", "yesOrNo7", "yesOrNo8", "yesOrNo9", "authorizingAuthority1",
            "authorizingAuthority2", "doctorName1", "doctorName2", "issuingAuthority1",
            "issuingAuthority2", "informationOfPhoto"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证海船普通船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validateSeaCrewCompetencyCertificate(String requestData) {
        System.out.println("\n===== 海船普通船员适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "certificateExpiringDate1", "certificateExpiringDate2", "dateOfIssue1", "dateOfIssue2",
            "capacity11", "capacity12", "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
            "issuingAuthority1", "issuingAuthority2", "officialUseOnly1", "officialUseOnly2",
            "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证功能记录
        validateFunctionRecords(requestData);
        
        // 验证等级和职务记录
        validateGradeAndCapacityRecords(requestData);
    }
    
    /**
     * 验证功能记录字段
     * @param requestData 请求数据JSON
     */
    private void validateFunctionRecords(String requestData) {
        System.out.println("\n===== 功能记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过function字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"function数字"格式的字段
                    if (name.matches("function\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何功能记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条功能记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "function" + recordNumber,
                    "level" + recordNumber,
                    "limitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("功能记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("功能记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证功能记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证等级和职务记录字段
     * @param requestData 请求数据JSON
     */
    private void validateGradeAndCapacityRecords(String requestData) {
        System.out.println("\n===== 等级和职务记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过gradwAndCapacity字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"gradwAndCapacity数字"格式的字段
                    if (name.matches("gradwAndCapacity\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(16);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何等级和职务记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条等级和职务记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "gradwAndCapacity" + recordNumber,
                    "alimitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("等级和职务记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("等级和职务记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证等级和职务记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证不参加航行和轮机值班海船船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validateNonWatchkeepingSeaCrewCertificate(String requestData) {
        System.out.println("\n===== 不参加航行和轮机值班海船船员适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "certificateExpiringDate1", "certificateExpiringDate2", "certificateIssuedDate1", 
            "certificateIssuedDate2", "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
            "issuingAuthority1", "issuingAuthority2", "officialUseOnly1", "officialUseOnly2",
            "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证职务和适用范围记录
        validateCapacityAndApplicationsRecords(requestData);
    }
    
    /**
     * 验证职务和适用范围记录字段
     * @param requestData 请求数据JSON
     */
    private void validateCapacityAndApplicationsRecords(String requestData) {
        System.out.println("\n===== 职务和适用范围记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过capacity字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"capacity数字"格式的字段
                    if (name.matches("capacity\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何职务和适用范围记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条职务和适用范围记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "capacity" + recordNumber,
                    "applivations" + recordNumber  // 注意：需求中是applivations，可能是拼写错误，应该是applications
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("职务和适用范围记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("职务和适用范围记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证职务和适用范围记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证海船高级船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validateSeniorSeaCrewCertificate(String requestData) {
        System.out.println("\n===== 海船高级船员适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "certificateExpiringDate1", "certificateExpiringDate2", "dateOfIssue1", "dateOfIssue2",
            "articleNumber1", "articleNumber2", "articleNumber3", "articleNumber4",
            "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
            "issuingAuthority1", "issuingAuthority2", "officialUseOnly1", "officialUseOnly2",
            "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证功能记录
        validateSeniorFunctionRecords(requestData);
        
        // 验证职务记录
        validateSeniorCapacityRecords(requestData);
    }
    
    /**
     * 验证海船高级船员功能记录字段
     * @param requestData 请求数据JSON
     */
    private void validateSeniorFunctionRecords(String requestData) {
        System.out.println("\n===== 海船高级船员功能记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过function字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"function数字"格式的字段
                    if (name.matches("function\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何功能记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条功能记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "function" + recordNumber,
                    "level" + recordNumber,
                    "limitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("功能记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("功能记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证海船高级船员功能记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证海船高级船员职务记录字段
     * @param requestData 请求数据JSON
     */
    private void validateSeniorCapacityRecords(String requestData) {
        System.out.println("\n===== 海船高级船员职务记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过capacity字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"capacity数字"格式的字段
                    if (name.matches("capacity\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何职务记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条职务记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "capacity" + recordNumber,
                    "alimitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("职务记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("职务记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证海船高级船员职务记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证船上厨师培训合格证明
     * @param requestData 请求数据JSON
     */
    private void validateShipCookCertificate(String requestData) {
        System.out.println("\n===== 船上厨师培训合格证明验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "dateOfIssue1", "dateOfIssue2", "nameOfTheTraingManager1", "nameOfTheTraingManager2",
            "issuingBody1", "issuingBody2", "photo"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证小型海船适任证书
     * @param requestData 请求数据JSON
     */
    private void validateSmallSeaVesselCertificate(String requestData) {
        System.out.println("\n===== 小型海船适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "certificateExpiringDate1", "certificateExpiringDate2", "dateOfIssue1", "dateOfIssue2",
            "articleNumber1", "articleNumber2", "articleNumber3", "articleNumber4",
            "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
            "issuingAuthority1", "issuingAuthority2", "officialUseOnly1", "officialUseOnly2",
            "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证功能记录
        validateSmallVesselFunctionRecords(requestData);
        
        // 验证等级和职务记录
        validateSmallVesselGradeAndCapacityRecords(requestData);
    }
    
    /**
     * 验证小型海船功能记录字段
     * @param requestData 请求数据JSON
     */
    private void validateSmallVesselFunctionRecords(String requestData) {
        System.out.println("\n===== 小型海船功能记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过function字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"function数字"格式的字段
                    if (name.matches("function\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何功能记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条功能记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "function" + recordNumber,
                    "level" + recordNumber,
                    "limitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("功能记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("功能记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证小型海船功能记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证小型海船等级和职务记录字段
     * @param requestData 请求数据JSON
     */
    private void validateSmallVesselGradeAndCapacityRecords(String requestData) {
        System.out.println("\n===== 小型海船等级和职务记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过gradwAndCapacity字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"gradwAndCapacity数字"格式的字段
                    if (name.matches("gradwAndCapacity\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(16);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何等级和职务记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条等级和职务记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "gradwAndCapacity" + recordNumber,
                    "alimitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("等级和职务记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("等级和职务记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证小型海船等级和职务记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证海上非自航船舶船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validateNonSelfPropelledVesselCertificate(String requestData) {
        System.out.println("\n===== 海上非自航船舶船员适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "certificateNo", "fullNameoftheHolder", "dateOfBirth", "placeOfBirth",
            "dateOfExpirty", "dateOfIssue", "nameOfDulyAuthorizedOfficial", 
            "remark", "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证船舶类型、等级和职务记录
        validateShipTypeAndCapacityRecords(requestData);
    }
    
    /**
     * 验证船舶类型、等级和职务记录字段
     * @param requestData 请求数据JSON
     */
    private void validateShipTypeAndCapacityRecords(String requestData) {
        System.out.println("\n===== 船舶类型、等级和职务记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过shipType字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"shipType数字"格式的字段
                    if (name.matches("shipType\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(8);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何船舶类型、等级和职务记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条船舶类型、等级和职务记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "shipType" + recordNumber,
                    "level" + recordNumber,
                    "capacity" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("船舶类型、等级和职务记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("船舶类型、等级和职务记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证船舶类型、等级和职务记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证公务船船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validateOfficialVesselCertificate(String requestData) {
        System.out.println("\n===== 公务船船员适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "certificateExpiringDate1", "certificateExpiringDate2", "dateOfIssue1", "dateOfIssue2",
            "issuingAuthority1", "issuingAuthority2", "officialUseOnly1", "officialUseOnly2",
            "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证等级和职务记录
        validateOfficialVesselGradeAndCapacityRecords(requestData);
    }
    
    /**
     * 验证公务船船员等级和职务记录字段
     * @param requestData 请求数据JSON
     */
    private void validateOfficialVesselGradeAndCapacityRecords(String requestData) {
        System.out.println("\n===== 公务船船员等级和职务记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过gradwAndCapacity字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"gradwAndCapacity数字"格式的字段
                    if (name.matches("gradwAndCapacity\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(16);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何等级和职务记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条等级和职务记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "gradwAndCapacity" + recordNumber,
                    "alimitationsApplying" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("等级和职务记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("等级和职务记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证公务船船员等级和职务记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证引航员船员适任证书
     * @param requestData 请求数据JSON
     */
    private void validatePilotCertificate(String requestData) {
        System.out.println("\n===== 引航员船员适任证书验证 =====");
        
        // 基本必需字段列表
        List<String> basicRequiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "certificateExpiringDate1", "certificateExpiringDate2", "dateOfIssue1", "dateOfIssue2",
            "remarks", "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
            "issuingAuthority1", "issuingAuthority2", "informationOfPhoto"
        );
        
        // 检查基本缺失字段
        List<String> missingBasicFields = JsonUtil.checkMissingFields(requestData, basicRequiredFields);
        if (!missingBasicFields.isEmpty()) {
            System.out.println("\n缺少以下基本必需字段:");
            for (String field : missingBasicFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有基本必需字段都存在");
        }
        
        // 验证引航类型、等级、区域和限制记录
        validatePilotRecords(requestData);
    }
    
    /**
     * 验证引航员记录字段
     * @param requestData 请求数据JSON
     */
    private void validatePilotRecords(String requestData) {
        System.out.println("\n===== 引航员记录验证 =====");
        
        try {
            JsonNode rootNode = JsonUtil.parseJson(requestData);
            if (rootNode == null || !rootNode.has("surface") || !rootNode.get("surface").isArray()) {
                System.out.println("JSON中不包含surface数组或解析失败");
                return;
            }
            
            JsonNode surfaceArray = rootNode.get("surface");
            
            // 查找所有记录编号（通过type字段确定）
            Set<Integer> recordNumbers = new HashSet<>();
            for (JsonNode item : surfaceArray) {
                if (item.has("name")) {
                    String name = item.get("name").asText();
                    // 使用正则表达式匹配"type数字"格式的字段
                    if (name.matches("type\\d+")) {
                        // 提取数字部分
                        String numberStr = name.substring(4);
                        try {
                            int recordNumber = Integer.parseInt(numberStr);
                            recordNumbers.add(recordNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀
                        }
                    }
                }
            }
            
            if (recordNumbers.isEmpty()) {
                System.out.println("未找到任何引航员记录");
                return;
            }
            
            System.out.println("找到 " + recordNumbers.size() + " 条引航员记录");
            
            // 对每条记录进行验证
            for (Integer recordNumber : recordNumbers) {
                List<String> recordFields = Arrays.asList(
                    "type" + recordNumber,
                    "level" + recordNumber,
                    "pilotageArea" + recordNumber,
                    "linitationOfPilotage" + recordNumber
                );
                
                List<String> missingFields = new ArrayList<>();
                for (String field : recordFields) {
                    boolean fieldExists = false;
                    for (JsonNode item : surfaceArray) {
                        if (item.has("name") && item.get("name").asText().equals(field)) {
                            fieldExists = true;
                            break;
                        }
                    }
                    
                    if (!fieldExists) {
                        missingFields.add(field);
                    }
                }
                
                if (missingFields.isEmpty()) {
                    System.out.println("引航员记录 #" + recordNumber + " 完整");
                } else {
                    System.out.println("引航员记录 #" + recordNumber + " 不完整，缺少字段: " + missingFields);
                }
            }
            
        } catch (Exception e) {
            System.out.println("验证引航员记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证特定航线江海直达船舶船员行驶资格证明培训合格证
     * @param requestData 请求数据JSON
     */
    private void validateSpecificRouteTrainingCertificate(String requestData) {
        System.out.println("\n===== 特定航线江海直达船舶船员行驶资格证明培训合格证验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "name", "numberOfCertificate", "dateofBirth", "creditCode", "gender", 
            "dateOfIssue", "applivations", "limitationsApplying", "expiryDate", 
            "issuingAuthority", "issuingDate", "photo"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证船上膳食服务辅助人员培训证明
     * @param requestData 请求数据JSON
     */
    private void validateShipMealServiceAssistantCertificate(String requestData) {
        System.out.println("\n===== 船上膳食服务辅助人员培训证明验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "fullNameoftheHolder1", "fullNameoftheHolder2", "nationality1", "nationality2",
            "dateOfBirth1", "dateOfBirth2", "gender1", "gender2", "certificateNo",
            "dateOfIssue1", "dateOfIssue2", "nameOfTheTraingManager1", "nameOfTheTraingManager2",
            "issuingBody1", "issuingBody2", "photo"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证船员培训质量管理体系证书
     * @param requestData 请求数据JSON
     */
    private void validateCrewTrainingQualityManagementCertificate(String requestData) {
        System.out.println("\n===== 船员培训质量管理体系证书验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "number1", "fullNameoftheHolder1", "fullNameoftheHolder2", "year1", "month1", "day1",
            "certificateExpiringDate", "evaluationOrganization1", "evaluationOrganization2",
            "dateOfIssue1", "dateOfIssue2", "number2", 
            "typeOfTheEvaluation1", "typeOfTheEvaluation2", "typeOfTheEvaluation3", 
            "typeOfTheEvaluation4", "typeOfTheEvaluation5", "typeOfTheEvaluation6",
            "resultOfEvaluation1", "resultOfEvaluation2", "resultOfEvaluation3", 
            "resultOfEvaluation4", "resultOfEvaluation5", "resultOfEvaluation6",
            "evaluationDate1", "evaluationDate2", "evaluationDate3", 
            "evaluationDate4", "evaluationDate5", "evaluationDate6",
            "evaOranization1", "evaOranization2", "evaOranization4", 
            "evaOranization5", "evaOranization6"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证海上设施工作人员海上交通安全技能培训合格证明
     * @param requestData 请求数据JSON
     */
    private void validateMaritimeFacilityWorkerSafetyCertificate(String requestData) {
        System.out.println("\n===== 海上设施工作人员海上交通安全技能培训合格证明验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "fullNameoftheHolder", "IDNumber", "passportNumber", "nationality", 
            "dateOfBirth", "gender", "certificateNo", "photo", 
            "trainingOrganization1", "trainingOrganization2", 
            "year1", "month1", "day1", 
            "nameofOrganization", "year2", "month2", "day2"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
    
    /**
     * 验证内河船员培训许可证
     * @param requestData 请求数据JSON
     */
    private void validateInlandCrewTrainingPermit(String requestData) {
        System.out.println("\n===== 内河船员培训许可证验证 =====");
        
        // 必需字段列表
        List<String> requiredFields = Arrays.asList(
            "permitNumber1", "anThorityName1", "trainingInstitutionCode1", "representative1", 
            "trainingProgram1", "trainingProgram2", "registeredAddress1", "trainingLocation1", 
            "periodOfValidity1", "periodOfValidity2", "issuingAuthority1", "dateofIssue1", 
            "anThorityName2", "permitNumber2", "registeredAddress2", "representative2", 
            "trainingLocation2", "periodOfValidity3", "periodOfValidity4", "remarks", 
            "number1", "number2", "number3", "number4", "number5", "number6", "number7", 
            "number8", "number9", "number10", "number11", 
            "atrainingProgram1", "atrainingProgram2", "atrainingProgram3", "atrainingProgram4", 
            "atrainingProgram5", "atrainingProgram6", "atrainingProgram7", "atrainingProgram8", 
            "atrainingProgram9", "atrainingProgram10", "atrainingProgram11", 
            "trainingScale1", "trainingScale2", "trainingScale3", "trainingScale4", 
            "trainingScale5", "trainingScale6", "trainingScale7", "trainingScale8", 
            "trainingScale9", "trainingScale10", "trainingScale11", 
            "issuingAuthority2", "dateofIssue2"
        );
        
        // 检查缺失字段
        List<String> missingFields = JsonUtil.checkMissingFields(requestData, requiredFields);
        if (!missingFields.isEmpty()) {
            System.out.println("\n缺少以下必需字段:");
            for (String field : missingFields) {
                System.out.println("- " + field);
            }
        } else {
            System.out.println("\n所有必需字段都存在");
        }
    }
} 