package com.js.hszpt.certificate.util;

/**
 * 命令行参数处理工具类
 */
public class CommandLineUtil {
    
    /**
     * 验证命令行参数
     * @param args 命令行参数
     * @return 参数是否有效
     */
    public static boolean validateArgs(String[] args) {
        if (args.length < 3) {
            System.out.println("参数不足，请按照以下格式传入参数：");
            printUsage();
            return false;
        }
        
        // 验证时间区间格式
        String timeRange = args[0];
        if (!timeRange.matches("\\d{8}-\\d{8}")) {
            System.out.println("时间区间格式错误，正确格式为：yyyyMMdd-yyyyMMdd");
            printUsage();
            return false;
        }
        
        // 验证第二个参数
        if (!"0".equals(args[1]) && !"1".equals(args[1])) {
            System.out.println("第二个参数 isDetail 必须为 0 或 1");
            printUsage();
            return false;
        }
        
        // 验证第三个参数
        if (!"0".equals(args[2]) && !"1".equals(args[2])) {
            System.out.println("第三个参数 isDownload 必须为 0 或 1");
            printUsage();
            return false;
        }
        
        return true;
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("正确的使用方式：");
        System.out.println("java -jar certificate-tool.jar <time_range> <isDetail> <isDownload>");
        System.out.println("参数说明：");
        System.out.println("  time_range: 时间区间，格式为yyyyMMdd-yyyyMMdd，例如：20250525-20250526，必填参数");
        System.out.println("  isDetail: 是否打印明细，0-不打印 1-打印，必填参数");
        System.out.println("  isDownload: 是否下载证书，0-不下载 1-下载，必填参数");
        System.out.println("示例：");
        System.out.println("  java -jar certificate-tool.jar 20250525-20250526 1 0");
    }
} 