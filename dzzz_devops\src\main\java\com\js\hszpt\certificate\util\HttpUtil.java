package com.js.hszpt.certificate.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * HTTP请求工具类
 */
public class HttpUtil {
    private static final ObjectMapper mapper = new ObjectMapper();
    
    /**
     * 发送GET请求并返回响应内容
     * @param urlString 请求URL
     * @return 响应内容
     */
    public static String sendGetRequest(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (java.io.InputStream is = connection.getInputStream()) {
                    java.io.ByteArrayOutputStream result = new java.io.ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = is.read(buffer)) != -1) {
                        result.write(buffer, 0, length);
                    }
                    return result.toString("UTF-8");
                }
            } else {
                System.out.println("HTTP请求失败，响应码: " + responseCode);
                return null;
            }
        } catch (IOException e) {
            System.out.println("发送HTTP请求失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 下载文件
     * @param urlString 文件URL
     * @param fileName 保存的文件名
     * @return 是否下载成功
     */
    public static boolean downloadFile(String urlString, String fileName) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(30000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 创建目录（如果不存在）
                Path filePath = Paths.get(fileName);
                Files.createDirectories(filePath.getParent());
                
                // 下载文件
                try (BufferedInputStream in = new BufferedInputStream(connection.getInputStream());
                     FileOutputStream out = new FileOutputStream(fileName)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
                System.out.println("文件已下载到: " + fileName);
                return true;
            } else {
                System.out.println("下载文件失败，响应码: " + responseCode);
                return false;
            }
        } catch (IOException e) {
            System.out.println("下载文件失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 下载证书文件
     * @param certificateId 证书ID
     * @return 是否下载成功
     */
    public static boolean downloadCertificate(String certificateId) {
        try {
            // 获取证书详情
            String detailUrl = "http://127.0.0.1:8282/certificate/getPermitDetail/" + certificateId;
            String detailResponse = sendGetRequest(detailUrl);
            if (detailResponse == null) {
                return false;
            }
            
            // 解析响应获取下载URL
            JsonNode detailJson = mapper.readTree(detailResponse);
            if (detailJson.has("result")) {
                String downloadUrl = detailJson.get("result").asText();
                String fileName = "downloads/certificate_" + certificateId + "_detail.ofd";
                return downloadFile(downloadUrl, fileName);
            } else {
                System.out.println("获取证书详情失败，响应中没有result字段");
                return false;
            }
        } catch (IOException e) {
            System.out.println("下载证书详情失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 下载证书图片
     * @param certificateId 证书ID
     * @return 是否下载成功
     */
    public static boolean downloadCertificateImage(String certificateId) {
        try {
            // 获取证书图片URL
            String imageUrlApi = "http://127.0.0.1:8282/certificate/download/" + certificateId;
            String imageResponse = sendGetRequest(imageUrlApi);
            if (imageResponse == null) {
                return false;
            }
            
            // 解析响应获取图片URL
            JsonNode imageJson = mapper.readTree(imageResponse);
            if (imageJson.has("jpg_url")) {
                String jpgUrl = imageJson.get("jpg_url").asText();
                String fileName = "downloads/certificate_" + certificateId + "_image.jpg";
                return downloadFile(jpgUrl, fileName);
            } else {
                System.out.println("获取证书图片失败，响应中没有jpg_url字段");
                return false;
            }
        } catch (IOException e) {
            System.out.println("下载证书图片失败: " + e.getMessage());
            return false;
        }
    }
} 