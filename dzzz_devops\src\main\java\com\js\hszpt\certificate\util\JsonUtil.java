package com.js.hszpt.certificate.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON工具类
 * 提供JSON格式化、解析和验证功能
 */
public class JsonUtil {
    private static final ObjectMapper mapper = new ObjectMapper();
    
    static {
        // 设置美化输出
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
    }
    
    /**
     * 格式化JSON字符串
     * @param json 原始JSON字符串
     * @return 格式化后的JSON字符串
     */
    public static String formatJson(String json) {
        try {
            Object jsonObj = mapper.readValue(json, Object.class);
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObj);
        } catch (IOException e) {
            System.out.println("JSON格式化失败: " + e.getMessage());
            return json;
        }
    }
    
    /**
     * 解析JSON字符串为JsonNode对象
     * @param json JSON字符串
     * @return JsonNode对象
     */
    public static JsonNode parseJson(String json) {
        try {
            return mapper.readTree(json);
        } catch (JsonProcessingException e) {
            System.out.println("JSON解析失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取JSON中值为空的字段
     * @param json JSON字符串
     * @return 值为空的字段列表
     */
    public static List<String> getEmptyValueFields(String json) {
        List<String> emptyFields = new ArrayList<>();
        try {
            JsonNode rootNode = mapper.readTree(json);
            if (rootNode.isArray()) {
                for (JsonNode item : rootNode) {
                    if (item.has("name") && item.has("value")) {
                        String name = item.get("name").asText();
                        JsonNode valueNode = item.get("value");
                        if (valueNode.isNull() || valueNode.asText().isEmpty()) {
                            emptyFields.add(name);
                        }
                    }
                }
            } else if (rootNode.has("surface") && rootNode.get("surface").isArray()) {
                JsonNode surfaceArray = rootNode.get("surface");
                for (JsonNode item : surfaceArray) {
                    if (item.has("name") && item.has("value")) {
                        String name = item.get("name").asText();
                        JsonNode valueNode = item.get("value");
                        if (valueNode.isNull() || valueNode.asText().isEmpty()) {
                            emptyFields.add(name);
                        }
                    }
                }
            }
        } catch (JsonProcessingException e) {
            System.out.println("JSON解析失败: " + e.getMessage());
        }
        return emptyFields;
    }
    
    /**
     * 检查JSON中是否缺少指定字段
     * @param json JSON字符串
     * @param fields 需要检查的字段列表
     * @return 缺少的字段列表
     */
    public static List<String> checkMissingFields(String json, List<String> fields) {
        List<String> missingFields = new ArrayList<>(fields);
        try {
            JsonNode rootNode = mapper.readTree(json);
            if (rootNode.has("surface") && rootNode.get("surface").isArray()) {
                JsonNode surfaceArray = rootNode.get("surface");
                for (JsonNode item : surfaceArray) {
                    if (item.has("name")) {
                        String name = item.get("name").asText();
                        missingFields.remove(name);
                    }
                }
            }
        } catch (JsonProcessingException e) {
            System.out.println("JSON解析失败: " + e.getMessage());
        }
        return missingFields;
    }
    
    /**
     * 验证JSON中是否包含指定的字段组
     * @param json JSON字符串
     * @param fieldGroups 字段组列表
     * @return 验证结果
     */
    public static String validateFieldGroups(String json, List<List<String>> fieldGroups) {
        StringBuilder result = new StringBuilder();
        try {
            JsonNode rootNode = mapper.readTree(json);
            if (rootNode.has("surface") && rootNode.get("surface").isArray()) {
                JsonNode surfaceArray = rootNode.get("surface");
                
                // 检查每组字段
                for (List<String> group : fieldGroups) {
                    boolean groupComplete = true;
                    List<String> missingFields = new ArrayList<>();
                    
                    for (String field : group) {
                        boolean fieldExists = false;
                        for (JsonNode item : surfaceArray) {
                            if (item.has("name") && item.get("name").asText().equals(field)) {
                                fieldExists = true;
                                break;
                            }
                        }
                        
                        if (!fieldExists) {
                            groupComplete = false;
                            missingFields.add(field);
                        }
                    }
                    
                    if (!groupComplete) {
                        result.append("缺少字段: ").append(missingFields).append("\n");
                    }
                }
                
                if (result.length() == 0) {
                    result.append("所有字段组都完整\n");
                }
            } else {
                result.append("JSON中不包含surface数组\n");
            }
        } catch (JsonProcessingException e) {
            result.append("JSON解析失败: ").append(e.getMessage()).append("\n");
        }
        return result.toString();
    }
} 