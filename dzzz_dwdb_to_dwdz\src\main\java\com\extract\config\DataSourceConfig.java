package com.extract.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "com.extract.mapper.standard", sqlSessionTemplateRef = "standardSqlSessionTemplate")
@MapperScan(basePackages = "com.extract.mapper.theme", sqlSessionTemplateRef = "themeSqlSessionTemplate")
public class DataSourceConfig {

    @Primary
    @Bean(name = "standardDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.standard")
    public DataSource standardDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = "themeDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.theme")
    public DataSource themeDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Primary
    @Bean(name = "standardSqlSessionFactory")
    public SqlSessionFactory standardSqlSessionFactory(@Qualifier("standardDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        bean.setMapperLocations(resolver.getResources("classpath:mapper/standard/*.xml"));
        
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
        configuration.setMapUnderscoreToCamelCase(false);
        bean.setConfiguration(configuration);
        
        return bean.getObject();
    }

    @Bean(name = "themeSqlSessionFactory")
    public SqlSessionFactory themeSqlSessionFactory(@Qualifier("themeDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        bean.setMapperLocations(resolver.getResources("classpath:mapper/theme/*.xml"));
        
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
        configuration.setMapUnderscoreToCamelCase(false);
        bean.setConfiguration(configuration);
        
        return bean.getObject();
    }

    @Primary
    @Bean(name = "standardTransactionManager")
    public DataSourceTransactionManager standardTransactionManager(@Qualifier("standardDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "themeTransactionManager")
    public DataSourceTransactionManager themeTransactionManager(@Qualifier("themeDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Primary
    @Bean(name = "standardSqlSessionTemplate")
    public SqlSessionTemplate standardSqlSessionTemplate(@Qualifier("standardSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "themeSqlSessionTemplate")
    public SqlSessionTemplate themeSqlSessionTemplate(@Qualifier("themeSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
} 