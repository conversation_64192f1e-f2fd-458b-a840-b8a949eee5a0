package com.extract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("dwdb_certificate_data")
public class CertificateData {
    @TableId("data_id")
    private String dataId;                    // 证照数据主键
    @TableField("certificate_id")
    private String certificateId;             // 电子证照业务标识码
    @TableField("template_id")
    private String templateId;                // 模板ID
    @TableField("certificate_type_name")
    private String certificateTypeName;       // 证照类型名称
    @TableField("certificate_type_code")
    private String certificateTypeCode;       // 证照类型代码，21位编码
    @TableField("certificate_define_authority_name")
    private String certificateDefineAuthorityName;    // 证照定义机构
    @TableField("certificate_define_authority_code")
    private String certificateDefineAuthorityCode;    // 证照定义机构代码（统一社会信用代码）
    @TableField("related_item_name")
    private String relatedItemName;           // 关联事项名称
    @TableField("related_item_code")
    private String relatedItemCode;           // 关联事项编码
    @TableField("certificate_holder_category")
    private String certificateHolderCategory; // 持证主体类别：1-自然人 2-法人或其他组织 3-混合 4-其他
    @TableField("certificate_holder_category_name")
    private String certificateHolderCategoryName; // 持证主体类别名称：自然人/法人或其他组织/混合/其他
    @TableField("validity_range")
    private String validityRange;             // 有效期限范围，多个用^分隔不同期限
    @TableField("certificate_identifier")
    private String certificateIdentifier;     // 电子证照唯一标识码
    @TableField("certificate_name")
    private String certificateName;           // 证照名称
    @TableField("certificate_number")
    private String certificateNumber;         // 证照编号
    @TableField("certificate_issuing_authority_name")
    private String certificateIssuingAuthorityName;   // 证照颁发机构
    @TableField("certificate_issuing_authority_code")
    private String certificateIssuingAuthorityCode;   // 证照颁发机构代码（统一社会信用代码）
    @TableField("certificate_issued_date")
    private String certificateIssuedDate;     // 证照颁发日期（yyyy-mm-dd）
    @TableField("certificate_holder_name")
    private String certificateHolderName;     // 持证主体名称（自然人姓名/法人全称）
    @TableField("certificate_holder_code")
    private String certificateHolderCode;     // 持证主体代码（信用代码/身份证号等）
    @TableField("certificate_holder_type_name")
    private String certificateHolderTypeName; // 持证主体代码类型：统一社会信用代码/公民身份号码/护照号/其他
    @TableField("certificate_effective_date")
    private String certificateEffectiveDate;  // 证照有效期开始日期（yyyy-mm-dd）
    @TableField("certificate_expiring_date")
    private String certificateExpiringDate;   // 证照有效期截止日期（yyyy-mm-dd或"长期"）
    @TableField("issue_dept_code2")
    private String issueDeptCode2;            // 证照颁发机构代码（海事内部统一编码2.0版本）
    @TableField("issue_dept_code3")
    private String issueDeptCode3;            // 证照颁发机构代码（海事内部统一编码3.0版本）
    @TableField("certificate_area_code")
    private String certificateAreaCode;       // 证照所属地区编码
    @TableField("surface_data")
    private String surfaceData;               // 照面信息
    @TableField("certificate_status")
    private String certificateStatus;         // 证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
    @TableField("creator_id")
    private String creatorId;                 // 登记人员ID
    @TableField("create_time")
    private String createTime;                // 创建时间
    @TableField("operator_id")
    private String operatorId;                // 最后操作人
    @TableField("update_time")
    private String updateTime;                // 修改时间
    @TableField("file_path")
    private String filePath;                  // 文件保存位置
    @TableField("sync_status")
    private String syncStatus;                // 同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
    @TableField("remarks")
    private String remarks;                   // 备注
    @TableField("dept_id")
    private String deptId;                    // 登记部门
    @TableField("apply_num")
    private String applyNum;                  // 申请编号
    @TableField("affair_type")
    private String affairType;                // 事项类型
    @TableField("serve_business")
    private String serveBusiness;             // 服务对象
    @TableField("affair_id")
    private String affairId;                  // 事项ID
    @TableField("affair_num")
    private String affairNum;                 // 事项编号
    @TableField("qz_type")
    private String qzType;                    // 签章类型
    @TableField("draft_url")
    private String draftUrl;                  // 草稿地址
    @TableField("sort_name")
    private String sortName;                  // 归档编号
    @TableField("sealname")
    private String sealname;                  // 印章名称
    @TableField("source_code")
    private String sourceCode;                // 源系统代码
    @TableField("rec_create_date")
    private LocalDateTime recCreateDate;      // 记录创建日期
    @TableField("rec_modify_date")
    private LocalDateTime recModifyDate;      // 记录修改日期
    @TableField("msa_org_code")
    private String msaOrgCode;               // 数据归属机构代码
} 