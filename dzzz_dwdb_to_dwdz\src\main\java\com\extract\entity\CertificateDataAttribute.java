package com.extract.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("dwdz_certificate_data_attribute")
public class CertificateDataAttribute {
    @TableId("certificate_attribute_id")
    private String certificateAttributeId;    // 证照照面数据主键
    
    @TableField("data_id")
    private String dataId;                    // 证照数据主键
    
    @TableField("attribute_column_name")
    private String attributeColumnName;       // 属性字段名称
    
    @TableField("attribute_value")
    private String attributeValue;            // 属性值
    
    @TableField("rec_create_date")
    private LocalDateTime recCreateDate;      // 记录创建日期
    
    @TableField("rec_modify_date")
    private LocalDateTime recModifyDate;      // 记录修改日期
} 