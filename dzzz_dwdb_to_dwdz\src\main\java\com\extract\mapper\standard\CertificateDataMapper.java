package com.extract.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.extract.entity.CertificateData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.time.LocalDateTime;
import java.util.List;

public interface CertificateDataMapper extends BaseMapper<CertificateData> {
    
    List<CertificateData> selectIncrementalData(@Param("lastCompletedTime") LocalDateTime lastCompletedTime,
                                               @Param("currentTime") LocalDateTime currentTime);

    @Select("SELECT MAX(rec_modify_date) FROM dwdb_certificate_data")
    LocalDateTime getCurrentMaxModifyDate();
} 