package com.extract.mapper.theme;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.extract.entity.ThemeCertificateData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.time.LocalDateTime;

public interface ThemeCertificateDataMapper extends BaseMapper<ThemeCertificateData> {
    
    @Select("SELECT last_completed_time FROM data_reception_tasks WHERE task_name = #{taskName}")
    LocalDateTime getLastCompletedTime(@Param("taskName") String taskName);
    
    void updateLastCompletedTime(@Param("taskName") String taskName, 
                               @Param("lastCompletedTime") LocalDateTime lastCompletedTime);
} 