package com.extract.service.impl;

import com.extract.entity.CertificateData;
import com.extract.entity.CertificateDataAttribute;
import com.extract.entity.ThemeCertificateData;
import com.extract.mapper.standard.CertificateDataMapper;
import com.extract.mapper.theme.CertificateDataAttributeMapper;
import com.extract.mapper.theme.ThemeCertificateDataMapper;
import com.extract.service.CertificateDataExtractService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class CertificateDataExtractServiceImpl implements CertificateDataExtractService {

    private final CertificateDataMapper certificateDataMapper;
    private final ThemeCertificateDataMapper themeCertificateDataMapper;
    private final CertificateDataAttributeMapper attributeMapper;
    private final ObjectMapper objectMapper;
    private final String taskName;

    public CertificateDataExtractServiceImpl(CertificateDataMapper certificateDataMapper,
                                           ThemeCertificateDataMapper themeCertificateDataMapper,
                                           CertificateDataAttributeMapper attributeMapper,
                                           ObjectMapper objectMapper,
                                           @Value("${task.certificate.taskName}") String taskName) {
        this.certificateDataMapper = certificateDataMapper;
        this.themeCertificateDataMapper = themeCertificateDataMapper;
        this.attributeMapper = attributeMapper;
        this.objectMapper = objectMapper;
        this.taskName = taskName;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void extractAndCleanData() {
        log.info("开始执行证照数据抽取任务");
        
        // 获取上次完成时间
        LocalDateTime lastCompletedTime = themeCertificateDataMapper.getLastCompletedTime(taskName);
        if (lastCompletedTime == null) {
            log.error("任务[{}]的抽取配置为空，请先配置", taskName);
            return;
        }

        // 获取标准库中最新的修改时间
        LocalDateTime currentTime = certificateDataMapper.getCurrentMaxModifyDate();
        
        // 查询增量数据
        log.info("查询参数 - lastCompletedTime: {}, currentTime: {}, maxModifyDate: {}", 
            lastCompletedTime, currentTime, lastCompletedTime);
        List<CertificateData> incrementalData = certificateDataMapper.selectIncrementalData(lastCompletedTime, currentTime);
        log.info("查询结果 - 数据量: {}, 第一条数据: {}", incrementalData.size(), 
            incrementalData.isEmpty() ? "无" : incrementalData.get(0));

        LocalDateTime maxModifyDate = lastCompletedTime;
        int successCount = 0;
        int failCount = 0;

        // 处理每条数据
        for (CertificateData sourceData : incrementalData) {
            try {
                // 处理主表数据
                ThemeCertificateData targetData = new ThemeCertificateData();
                BeanUtils.copyProperties(sourceData, targetData);
                themeCertificateDataMapper.insert(targetData);

                // 处理照面信息
                if (StringUtils.hasText(sourceData.getSurfaceData())) {
                    processSurfaceData(sourceData.getDataId(), sourceData.getSurfaceData());
                }

                // 更新最大修改时间
                if (sourceData.getRecModifyDate().isAfter(maxModifyDate)) {
                    maxModifyDate = sourceData.getRecModifyDate();
                }
                
                successCount++;
            } catch (Exception e) {
                log.error("处理数据失败, dataId: {}, error: ", sourceData.getDataId(), e);
                failCount++;
                e.printStackTrace();
            }
        }

        // 更新任务完成时间
        themeCertificateDataMapper.updateLastCompletedTime(taskName, maxModifyDate);
        
        log.info("证照数据抽取任务完成, 总处理数据: {}, 成功: {}, 失败: {}", 
                incrementalData.size(), successCount, failCount);
    }

    private void processSurfaceData(String dataId, String surfaceData) throws JsonProcessingException {
        List<Map<String, String>> attributes = objectMapper.readValue(surfaceData, 
                new TypeReference<List<Map<String, String>>>() {});
        
        List<CertificateDataAttribute> attributeList = new ArrayList<>();
        for (Map<String, String> attr : attributes) {
            CertificateDataAttribute attribute = new CertificateDataAttribute();
            attribute.setCertificateAttributeId(UUID.randomUUID().toString());
            attribute.setDataId(dataId);
            attribute.setAttributeColumnName(attr.get("name"));
            attribute.setAttributeValue(attr.get("value"));
            attribute.setRecCreateDate(LocalDateTime.now());
            attribute.setRecModifyDate(LocalDateTime.now());
            attributeList.add(attribute);
        }
        
        if (!attributeList.isEmpty()) {
            attributeMapper.batchInsert(attributeList);
        }
    }
} 