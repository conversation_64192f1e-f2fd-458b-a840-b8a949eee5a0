package com.extract.task;

import com.extract.service.CertificateDataExtractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
public class CertificateDataExtractTask {

    private final CertificateDataExtractService extractService;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    public CertificateDataExtractTask(CertificateDataExtractService extractService) {
        this.extractService = extractService;
    }

    @Scheduled(cron = "${task.certificate.cron}")
    public void execute() {
        if (!isRunning.compareAndSet(false, true)) {
            log.info("上一次任务还在执行中，本次任务将不执行");
            return;
        }

        try {
            log.info("开始执行证照数据抽取定时任务");
            extractService.extractAndCleanData();
            log.info("证照数据抽取定时任务执行完成");
        } catch (Exception e) {
            log.error("证照数据抽取定时任务执行异常", e);
        } finally {
            isRunning.set(false);
        }
    }
} 