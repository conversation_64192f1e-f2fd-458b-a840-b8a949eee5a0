spring:
  datasource:
      
    # 标准库数据源配置
    standard:
      jdbc-url: *****************************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 60000

    # 主题库数据源配置
    theme:
      jdbc-url: *****************************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 60000

# 任务配置    
task:
  certificate:
    cron: "0 0/1 * * * ?" # 每1分钟执行一次
    taskName: "dwdb_certificate_data" 