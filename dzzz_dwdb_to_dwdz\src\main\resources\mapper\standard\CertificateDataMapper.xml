<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extract.mapper.standard.CertificateDataMapper">
    <resultMap id="BaseResultMap" type="com.extract.entity.CertificateData">
        <id column="data_id" property="dataId"/>
        <result column="certificate_id" property="certificateId"/>
        <result column="template_id" property="templateId"/>
        <result column="certificate_type_name" property="certificateTypeName"/>
        <result column="certificate_type_code" property="certificateTypeCode"/>
        <result column="certificate_define_authority_name" property="certificateDefineAuthorityName"/>
        <result column="certificate_define_authority_code" property="certificateDefineAuthorityCode"/>
        <result column="related_item_name" property="relatedItemName"/>
        <result column="related_item_code" property="relatedItemCode"/>
        <result column="certificate_holder_category" property="certificateHolderCategory"/>
        <result column="certificate_holder_category_name" property="certificateHolderCategoryName"/>
        <result column="validity_range" property="validityRange"/>
        <result column="certificate_identifier" property="certificateIdentifier"/>
        <result column="certificate_name" property="certificateName"/>
        <result column="certificate_number" property="certificateNumber"/>
        <result column="certificate_issuing_authority_name" property="certificateIssuingAuthorityName"/>
        <result column="certificate_issuing_authority_code" property="certificateIssuingAuthorityCode"/>
        <result column="certificate_issued_date" property="certificateIssuedDate"/>
        <result column="certificate_holder_name" property="certificateHolderName"/>
        <result column="certificate_holder_code" property="certificateHolderCode"/>
        <result column="certificate_holder_type_name" property="certificateHolderTypeName"/>
        <result column="certificate_effective_date" property="certificateEffectiveDate"/>
        <result column="certificate_expiring_date" property="certificateExpiringDate"/>
        <result column="issue_dept_code2" property="issueDeptCode2"/>
        <result column="issue_dept_code3" property="issueDeptCode3"/>
        <result column="certificate_area_code" property="certificateAreaCode"/>
        <result column="surface_data" property="surfaceData"/>
        <result column="certificate_status" property="certificateStatus"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="operator_id" property="operatorId"/>
        <result column="update_time" property="updateTime"/>
        <result column="file_path" property="filePath"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="remarks" property="remarks"/>
        <result column="dept_id" property="deptId"/>
        <result column="apply_num" property="applyNum"/>
        <result column="affair_type" property="affairType"/>
        <result column="serve_business" property="serveBusiness"/>
        <result column="affair_id" property="affairId"/>
        <result column="affair_num" property="affairNum"/>
        <result column="qz_type" property="qzType"/>
        <result column="draft_url" property="draftUrl"/>
        <result column="sort_name" property="sortName"/>
        <result column="sealname" property="sealname"/>
        <result column="source_code" property="sourceCode"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
        <result column="msa_org_code" property="msaOrgCode"/>
    </resultMap>

    <select id="selectIncrementalData" resultMap="BaseResultMap">
        SELECT * FROM dwdb_certificate_data 
        WHERE rec_modify_date &gt;= #{lastCompletedTime} 
        AND rec_modify_date &lt;= #{currentTime} 
        ORDER BY rec_modify_date ASC
    </select>
</mapper> 