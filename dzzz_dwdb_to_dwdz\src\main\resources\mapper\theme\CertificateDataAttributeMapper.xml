<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extract.mapper.theme.CertificateDataAttributeMapper">
    <resultMap id="BaseResultMap" type="com.extract.entity.CertificateDataAttribute">
        <id column="certificate_attribute_id" property="certificateAttributeId"/>
        <result column="data_id" property="dataId"/>
        <result column="attribute_column_name" property="attributeColumnName"/>
        <result column="attribute_value" property="attributeValue"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
    </resultMap>

    <insert id="insert" parameterType="com.extract.entity.CertificateDataAttribute">
        INSERT INTO dwdz_certificate_data_attribute (
            certificate_attribute_id,
            data_id,
            attribute_column_name,
            attribute_value,
            rec_create_date,
            rec_modify_date
        ) VALUES (
            #{certificateAttributeId},
            #{dataId},
            #{attributeColumnName},
            #{attributeValue},
            #{recCreateDate},
            #{recModifyDate}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO dwdz_certificate_data_attribute (
            certificate_attribute_id,
            data_id,
            attribute_column_name,
            attribute_value,
            rec_create_date,
            rec_modify_date
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (#{item.certificateAttributeId},
            #{item.dataId},
            #{item.attributeColumnName},
            #{item.attributeValue},
            #{item.recCreateDate},
            #{item.recModifyDate})
        </foreach>
    </insert>
</mapper> 