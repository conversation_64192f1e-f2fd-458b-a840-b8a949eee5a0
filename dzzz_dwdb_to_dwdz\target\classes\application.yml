spring:
  profiles:
    active: local

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - [%X{traceId}] - %msg%n"
  level:
    root: INFO
    com.example: DEBUG

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.example.entity

# 定时任务配置    
task:
  certificate:
    cron: "0 0 1 * * ?" # 每天凌晨1点执行
    taskName: "dwdb_certificate_data" # 任务名称 