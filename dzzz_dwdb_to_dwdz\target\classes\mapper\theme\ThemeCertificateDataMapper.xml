<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extract.mapper.theme.ThemeCertificateDataMapper">
    <resultMap id="BaseResultMap" type="com.extract.entity.ThemeCertificateData">
        <id column="data_id" property="dataId"/>
        <result column="certificate_id" property="certificateId"/>
        <result column="certificate_type_name" property="certificateTypeName"/>
        <result column="certificate_type_code" property="certificateTypeCode"/>
        <result column="certificate_define_authority_name" property="certificateDefineAuthorityName"/>
        <result column="certificate_define_authority_code" property="certificateDefineAuthorityCode"/>
        <result column="related_item_name" property="relatedItemName"/>
        <result column="related_item_code" property="relatedItemCode"/>
        <result column="certificate_holder_category" property="certificateHolderCategory"/>
        <result column="certificate_holder_category_name" property="certificateHolderCategoryName"/>
        <result column="validity_range" property="validityRange"/>
        <result column="certificate_identifier" property="certificateIdentifier"/>
        <result column="certificate_name" property="certificateName"/>
        <result column="certificate_number" property="certificateNumber"/>
        <result column="certificate_issuing_authority_name" property="certificateIssuingAuthorityName"/>
        <result column="certificate_issuing_authority_code" property="certificateIssuingAuthorityCode"/>
        <result column="certificate_issued_date" property="certificateIssuedDate"/>
        <result column="certificate_holder_name" property="certificateHolderName"/>
        <result column="certificate_holder_code" property="certificateHolderCode"/>
        <result column="certificate_holder_type_name" property="certificateHolderTypeName"/>
        <result column="certificate_effective_date" property="certificateEffectiveDate"/>
        <result column="certificate_expiring_date" property="certificateExpiringDate"/>
        <result column="issue_dept_code2" property="issueDeptCode2"/>
        <result column="issue_dept_code3" property="issueDeptCode3"/>
        <result column="certificate_area_code" property="certificateAreaCode"/>
        <result column="certificate_status" property="certificateStatus"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="operator_id" property="operatorId"/>
        <result column="update_time" property="updateTime"/>
        <result column="file_path" property="filePath"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="remarks" property="remarks"/>
        <result column="dept_id" property="deptId"/>
        <result column="apply_num" property="applyNum"/>
        <result column="affair_type" property="affairType"/>
        <result column="serve_business" property="serveBusiness"/>
        <result column="affair_id" property="affairId"/>
        <result column="affair_num" property="affairNum"/>
        <result column="sort_name" property="sortName"/>
        <result column="source_code" property="sourceCode"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
        <result column="msa_org_code" property="msaOrgCode"/>
    </resultMap>

    <insert id="insert" parameterType="com.extract.entity.ThemeCertificateData">
        INSERT INTO dwdz_certificate_data (
            data_id, certificate_id, certificate_type_name, certificate_type_code,
            certificate_define_authority_name, certificate_define_authority_code,
            related_item_name, related_item_code, certificate_holder_category,
            certificate_holder_category_name, validity_range, certificate_identifier,
            certificate_name, certificate_number, certificate_issuing_authority_name,
            certificate_issuing_authority_code, certificate_issued_date,
            certificate_holder_name, certificate_holder_code, certificate_holder_type_name,
            certificate_effective_date, certificate_expiring_date, issue_dept_code2,
            issue_dept_code3, certificate_area_code, certificate_status, creator_id,
            create_time, operator_id, update_time, file_path, sync_status, remarks,
            dept_id, apply_num, affair_type, serve_business, affair_id, affair_num,
            sort_name, cert_print_no, apply_id, ship_id, ship_name, ship_name_en,
            ship_imo, ship_call_sign, ship_mmsi, apply_type, apply_date,
            accept_org_code2, accept_date, appr_org_code2, appr_date,
            accept_org_code3, appr_org_code3, source_code, rec_create_date,
            rec_modify_date, msa_org_code
        ) VALUES (
            #{dataId}, #{certificateId}, #{certificateTypeName}, #{certificateTypeCode},
            #{certificateDefineAuthorityName}, #{certificateDefineAuthorityCode},
            #{relatedItemName}, #{relatedItemCode}, #{certificateHolderCategory},
            #{certificateHolderCategoryName}, #{validityRange}, #{certificateIdentifier},
            #{certificateName}, #{certificateNumber}, #{certificateIssuingAuthorityName},
            #{certificateIssuingAuthorityCode}, #{certificateIssuedDate},
            #{certificateHolderName}, #{certificateHolderCode}, #{certificateHolderTypeName},
            #{certificateEffectiveDate}, #{certificateExpiringDate}, #{issueDeptCode2},
            #{issueDeptCode3}, #{certificateAreaCode}, #{certificateStatus}, #{creatorId},
            #{createTime}, #{operatorId}, #{updateTime}, #{filePath}, #{syncStatus}, #{remarks},
            #{deptId}, #{applyNum}, #{affairType}, #{serveBusiness}, #{affairId}, #{affairNum},
            #{sortName}, #{certPrintNo}, #{applyId}, #{shipId}, #{shipName}, #{shipNameEn},
            #{shipImo}, #{shipCallSign}, #{shipMmsi}, #{applyType}, #{applyDate},
            #{acceptOrgCode2}, #{acceptDate}, #{apprOrgCode2}, #{apprDate},
            #{acceptOrgCode3}, #{apprOrgCode3}, #{sourceCode}, #{recCreateDate},
            #{recModifyDate}, #{msaOrgCode}
        )
    </insert>

    <update id="updateLastCompletedTime">
        UPDATE data_reception_tasks 
        SET last_completed_time = #{lastCompletedTime}
        WHERE task_name = #{taskName}
    </update>
</mapper> 