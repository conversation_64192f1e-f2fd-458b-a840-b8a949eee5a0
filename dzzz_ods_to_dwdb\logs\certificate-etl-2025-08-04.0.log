2025-08-04 09:47:59.805 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 11660 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-08-04 09:47:59.816 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-08-04 09:48:05.383 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-08-04 09:48:06.460 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-08-04 09:48:06.461 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 09:48:06.572 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-08-04 09:48:06.576 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 09:48:07.132 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:48:07.134 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:48:07.154 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:48:07.992 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-08-04 09:48:08.378 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-08-04 09:48:08.477 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 09:48:08.702 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 09:48:12.384 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-08-04 09:48:12.681 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-08-04 09:48:12.682 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-08-04 09:48:23.274 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-08-04 09:48:23.958 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-08-04 09:48:23.959 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-08-04 09:48:24.162 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-08-04 09:48:24.163 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-08-04 09:48:24.163 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-08-04 09:48:24.163 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-04 09:48:24.163 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-04 09:48:24.515 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 09:48:24.516 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-08-04 09:48:24.517 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 09:48:26.490 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 2327毫秒
2025-08-04 09:48:26.491 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-08-04 09:48:28.658 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 30.164 seconds (JVM running for 35.594)
2025-08-04 09:48:30.134 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-04 09:48:30.134 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-04 09:48:30.514 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 380毫秒
2025-08-04 09:48:30.997 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 09:48:31.019 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 09:48:31.020 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 09:48:31.250 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 09:49:12.621 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 27120 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-08-04 09:49:12.623 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-08-04 09:49:18.575 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-08-04 09:49:20.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-08-04 09:49:20.143 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 09:49:20.205 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-08-04 09:49:20.205 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 09:49:20.636 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:49:20.637 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:49:20.673 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:49:21.332 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-08-04 09:49:21.712 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-08-04 09:49:21.822 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 09:49:22.062 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 09:49:25.802 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-08-04 09:49:26.085 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-08-04 09:49:26.086 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-08-04 09:49:37.084 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-08-04 09:49:37.280 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-08-04 09:49:37.281 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-08-04 09:49:37.559 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-08-04 09:49:37.560 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-08-04 09:49:37.560 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-08-04 09:49:37.560 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-04 09:49:37.561 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-04 09:49:37.919 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 09:49:37.919 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-08-04 09:49:37.920 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 09:49:39.828 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 2267毫秒
2025-08-04 09:49:39.828 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-08-04 09:49:42.097 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 30.604 seconds (JVM running for 35.188)
2025-08-04 09:49:43.309 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 09:49:43.400 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 09:49:43.402 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 09:49:44.505 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 09:50:06.778 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 12924 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-08-04 09:50:06.781 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-08-04 09:50:09.697 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-08-04 09:50:10.803 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-08-04 09:50:10.804 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 09:50:10.881 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-08-04 09:50:10.881 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 09:50:11.264 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:50:11.266 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:50:11.286 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 09:50:11.886 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-08-04 09:50:12.121 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-08-04 09:50:12.196 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 09:50:12.312 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 09:50:15.651 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-08-04 09:50:15.990 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-08-04 09:50:15.991 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-08-04 09:50:26.848 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-08-04 09:50:27.077 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-08-04 09:50:27.078 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-08-04 09:50:27.492 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-08-04 09:50:27.493 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-08-04 09:50:27.493 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-08-04 09:50:27.493 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-04 09:50:27.493 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-04 09:50:27.890 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 09:50:27.891 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-08-04 09:50:27.892 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 09:50:29.862 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 2369毫秒
2025-08-04 09:50:29.863 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-08-04 09:50:35.013 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 29.391 seconds (JVM running for 33.159)
2025-08-04 09:50:37.364 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-04 09:50:37.365 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-04 09:50:37.783 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 418毫秒
2025-08-04 09:50:38.184 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 09:50:38.192 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 09:50:38.194 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 09:50:38.198 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 12:54:37.383 [main] INFO  c.e.certificate.CertificateEtlApplicationTest - Starting CertificateEtlApplicationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 7680 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-08-04 12:54:37.387 [main] INFO  c.e.certificate.CertificateEtlApplicationTest - The following 1 profile is active: "test"
2025-08-04 12:54:42.595 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-08-04 12:54:44.348 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-08-04 12:54:44.348 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 12:54:44.418 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-08-04 12:54:44.418 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 12:54:44.865 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 12:54:44.877 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 12:54:44.908 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-08-04 12:54:45.808 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-08-04 12:54:46.437 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-08-04 12:54:46.658 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 12:54:46.913 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 12:54:52.003 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-08-04 12:54:52.664 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-08-04 12:54:52.667 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-08-04 12:55:07.031 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-08-04 12:55:07.520 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-08-04 12:55:07.521 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-08-04 12:55:08.318 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-08-04 12:55:08.319 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-08-04 12:55:08.319 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-08-04 12:55:08.320 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-04 12:55:08.320 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-04 12:55:08.861 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-04 12:55:08.862 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-08-04 12:55:08.863 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-04 12:55:11.732 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 3412毫秒
2025-08-04 12:55:11.733 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-08-04 12:55:14.901 [main] INFO  c.e.certificate.CertificateEtlApplicationTest - Started CertificateEtlApplicationTest in 39.092 seconds (JVM running for 44.576)
2025-08-04 12:55:17.191 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 12:55:17.316 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-04 12:55:17.319 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-04 12:55:18.158 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
