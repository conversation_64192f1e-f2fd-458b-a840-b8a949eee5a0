2025-08-07 18:52:50.047 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 26244 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-08-07 18:52:50.077 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-08-07 18:52:57.104 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-08-07 18:53:00.216 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-08-07 18:53:00.217 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-07 18:53:00.314 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-08-07 18:53:00.314 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-07 18:53:00.891 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-08-07 18:53:00.892 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-08-07 18:53:00.938 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-08-07 18:53:01.988 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-08-07 18:53:02.297 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-08-07 18:53:02.433 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-07 18:53:02.840 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-07 18:53:08.178 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-08-07 18:53:08.706 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-08-07 18:53:08.707 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-08-07 18:53:20.300 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-08-07 18:53:20.678 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-08-07 18:53:20.679 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-08-07 18:53:21.123 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-08-07 18:53:21.123 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-08-07 18:53:21.123 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-08-07 18:53:21.124 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-08-07 18:53:21.125 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-08-07 18:53:21.618 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-08-07 18:53:21.619 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-08-07 18:53:21.620 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-08-07 18:53:23.887 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 2762毫秒
2025-08-07 18:53:23.888 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-08-07 18:53:28.085 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 39.941 seconds (JVM running for 47.138)
2025-08-07 18:53:30.021 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-07 18:53:30.037 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-08-07 18:53:30.038 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-08-07 18:53:32.136 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
