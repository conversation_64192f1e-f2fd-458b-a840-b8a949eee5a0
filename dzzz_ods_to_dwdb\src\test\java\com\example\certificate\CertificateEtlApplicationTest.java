package com.example.certificate;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.PrintStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试CertificateEtlApplication的日志目录创建功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class CertificateEtlApplicationTest {

    private final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    private final PrintStream originalErr = System.err;
    private Path testLogDir;

    @BeforeEach
    public void setUp() {
        System.setErr(new PrintStream(errContent));
        testLogDir = Paths.get("test-logs-" + System.currentTimeMillis());
    }

    @AfterEach
    public void tearDown() {
        System.setErr(originalErr);
        // 清理测试目录
        try {
            if (Files.exists(testLogDir)) {
                Files.deleteIfExists(testLogDir);
            }
        } catch (Exception e) {
            // 忽略清理错误
        }
    }

    /**
     * 测试日志目录创建成功的情况
     */
    @Test
    public void testLogDirectoryCreationSuccess() {
        File logDir = testLogDir.toFile();
        
        // 确保目录不存在
        assertFalse(logDir.exists());
        
        // 模拟应用程序的目录创建逻辑
        if (!logDir.exists()) {
            boolean created = logDir.mkdirs();
            if (!created) {
                System.err.println("Warning: Failed to create logs directory: " + logDir.getAbsolutePath());
            }
        }
        
        // 验证目录创建成功
        assertTrue(logDir.exists());
        assertTrue(logDir.isDirectory());
        
        // 验证没有错误信息输出
        String errorOutput = errContent.toString();
        assertFalse(errorOutput.contains("Warning: Failed to create logs directory"));
    }

    /**
     * 测试目录已存在的情况
     */
    @Test
    public void testLogDirectoryAlreadyExists() throws Exception {
        File logDir = testLogDir.toFile();
        
        // 先创建目录
        assertTrue(logDir.mkdirs());
        assertTrue(logDir.exists());
        
        // 模拟应用程序的目录创建逻辑
        if (!logDir.exists()) {
            boolean created = logDir.mkdirs();
            if (!created) {
                System.err.println("Warning: Failed to create logs directory: " + logDir.getAbsolutePath());
            }
        }
        
        // 验证目录仍然存在
        assertTrue(logDir.exists());
        assertTrue(logDir.isDirectory());
        
        // 验证没有错误信息输出（因为目录已存在，不需要创建）
        String errorOutput = errContent.toString();
        assertFalse(errorOutput.contains("Warning: Failed to create logs directory"));
    }

    /**
     * 测试应用程序可以正常启动
     */
    @Test
    public void testApplicationContextLoads() {
        // 这个测试验证Spring Boot应用程序上下文可以正常加载
        // 如果有任何配置问题，这个测试会失败
        assertDoesNotThrow(() -> {
            // Spring Boot测试框架会自动加载应用程序上下文
        });
    }
}
