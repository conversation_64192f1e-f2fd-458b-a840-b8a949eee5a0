package com.example.certificate.service;

import com.example.certificate.entity.aggregate.OdsCertificateData;
import com.example.certificate.entity.standard.*;
import com.example.certificate.mapper.aggregate.OdsCertificateDataMapper;
import com.example.certificate.util.CertificateConverter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.jdbc.core.JdbcTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import com.example.certificate.entity.BaseEntity;
import com.example.certificate.mapper.standard.DwdbCertificateDataMapper;
import java.util.Date;
import java.sql.Timestamp;

import java.util.List;
import java.util.UUID;
import java.util.Map;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.HashMap;
import org.apache.commons.lang3.StringUtils;
import java.text.SimpleDateFormat;
import java.text.ParseException;
// 在文件顶部的 import 部分添加以下语句
import org.springframework.context.annotation.Import;
import com.example.certificate.config.TestConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

/**
 * 证书ETL服务集成测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test") // 使用测试环境配置
@Import(TestConfig.class)
@Transactional // 测试完成后回滚事务，不影响数据库
public class CertificateEtlServiceIntegrationTest {

    @Autowired
    @Qualifier("aggregateJdbcTemplate")  // 使用汇聚库数据源
    private JdbcTemplate aggregateJdbcTemplate;
    
    @Autowired
    private CertificateEtlService certificateEtlService;
    
    @BeforeEach
    public void setUp() {
        System.out.println("=== 测试开始执行 ===");
    }

    @AfterEach
    public void tearDown() {
        System.out.println("=== 测试执行结束 ===");
    }

    /**
     * 测试ETL流程
     */
    @Test
    public void testEtlProcess() throws ParseException {
        // 1. 从测试表查询增量数据
        String testSql = "SELECT * FROM ods_certificate_data_test " +
                        "WHERE fcdc_date > ? AND fcdc_date <= ? " +
                        "ORDER BY fcdc_date";
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = sdf.parse("2021-09-01 00:00:00"); // 设置具体的开始时间
        Date endTime = sdf.parse("2021-09-01 23:59:59"); // 设置具体的结束时间
        
        List<Map<String, Object>> testData = aggregateJdbcTemplate.queryForList(
            testSql, startTime, endTime);
            
        // 2. 将测试数据写入ODS表
        String insertSql = "INSERT INTO ods_certificate_data (" +
            "dataid, certificateid, catalogid, catalogname, templateid, " +
            "certificatetype, certificatetypecode, issuedept, issuedeptcode, " +
            "certificateareacode, certificateholder, certificateholdercode, " +
            "certificateholdertype, certificatenumber, issuedate, validbegindate, " +
            "validenddate, surfacedata, status, creator, createtime, operator, " +
            "updatetime, filepath, syncstatus, remarks, deptid, applynum, " +
            "affairname, affairtype, servebusiness, affairid, affairnum, " +
            "qztype, zztype, drafturl, isview, sortname, col1, verifydate, " +
            "verification, creditcode, sealname, fcdc_date" +
            ") VALUES (" +
            "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "?, ?, ?, ?" +
            ")";
            
        for(Map<String, Object> row : testData) {
            aggregateJdbcTemplate.update(insertSql,
                row.get("dataid"),
                row.get("certificateid"),
                row.get("catalogid"),
                row.get("catalogname"),
                row.get("templateid"),
                row.get("certificatetype"),
                row.get("certificatetypecode"),
                row.get("issuedept"),
                row.get("issuedeptcode"),
                row.get("certificateareacode"),
                row.get("certificateholder"),
                row.get("certificateholdercode"),
                row.get("certificateholdertype"),
                row.get("certificatenumber"),
                row.get("issuedate"),
                row.get("validbegindate"),
                row.get("validenddate"),
                row.get("surfacedata"),
                row.get("status"),
                row.get("creator"),
                row.get("createtime"),
                row.get("operator"),
                row.get("updatetime"),
                row.get("filepath"),
                row.get("syncstatus"),
                row.get("remarks"),
                row.get("deptid"),
                row.get("applynum"),
                row.get("affairname"),
                row.get("affairtype"),
                row.get("servebusiness"),
                row.get("affairid"),
                row.get("affairnum"),
                row.get("qztype"),
                row.get("zztype"),
                row.get("drafturl"),
                row.get("isview"),
                row.get("sortname"),
                row.get("col1"),
                row.get("verifydate"),
                row.get("verification"),
                row.get("creditcode"),
                row.get("sealname"),
                row.get("fcdc_date")
            );
        }

        // 3. 执行ETL任务
        certificateEtlService.executeEtlTask("ods_certificate_data");

        // 4. 验证数据一致性
        for(Map<String, Object> sourceData : testData) {
            String certificateType = (String)sourceData.get("certificatetype");
            String dataId = (String)sourceData.get("dataid");

            // 输入验证：确保dataId不为空且格式正确，防止SQL注入
            if (dataId == null || dataId.trim().isEmpty()) {
                throw new IllegalArgumentException("Data ID cannot be null or empty");
            }

            // 验证dataId格式（假设应该是UUID格式或特定格式）
            if (!isValidDataId(dataId)) {
                throw new IllegalArgumentException("Invalid data ID format: " + dataId);
            }

            // 使用预编译的SQL查询，避免SQL注入风险
            String targetSql = getTargetTableSql(certificateType);
            Map<String, Object> targetData = executeSecureQuery(targetSql, dataId);

            assertFieldsEqual(sourceData, targetData, certificateType);
        }
    }

    /**
     * 允许的证书类型白名单，用于防止SQL注入
     */
    private static final Set<String> ALLOWED_CERTIFICATE_TYPES = new HashSet<>(Arrays.asList(
        "不参加航行和轮机值班海船船员适任证书",
        "船员适任证书申请表",
        "公务船船员适任证书",
        "海船高级船员适任证书",
        "海船普通船员适任证书",
        "海船船员培训合格证书",
        "海上非自航船舶船员适任证书",
        "海员外派机构资质证书",
        "海船船员健康证明",
        "内河船舶船员适任证书",
        "海船船员内河航线行驶资格证明",
        "内河船舶船员培训合格证",
        "船员培训质量管理体系证书",
        "海船船员培训许可证",
        "特定航线江海直达船舶船员行驶资格证明培训合格证",
        "小型海船适任证书",
        "引航员船员适任证书"
    ));

    /**
     * 验证数据ID格式是否有效，防止SQL注入
     * @param dataId 数据ID
     * @return 如果格式有效返回true，否则返回false
     */
    private boolean isValidDataId(String dataId) {
        if (dataId == null || dataId.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含SQL注入相关的危险字符
        String[] dangerousPatterns = {
            "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_",
            "union", "select", "insert", "update", "delete",
            "drop", "create", "alter", "exec", "execute"
        };

        String lowerDataId = dataId.toLowerCase();
        for (String pattern : dangerousPatterns) {
            if (lowerDataId.contains(pattern)) {
                return false;
            }
        }

        // 验证数据ID只包含字母、数字、连字符和下划线
        return dataId.matches("^[a-zA-Z0-9_-]+$");
    }

    /**
     * 安全执行SQL查询，使用预编译语句防止SQL注入
     * @param sql 预编译的SQL语句（包含?占位符）
     * @param dataId 数据ID参数
     * @return 查询结果
     */
    private Map<String, Object> executeSecureQuery(String sql, String dataId) {
        // 再次验证SQL语句的安全性
        if (sql == null || sql.trim().isEmpty()) {
            throw new IllegalArgumentException("SQL statement cannot be null or empty");
        }

        // 确保SQL语句只包含一个参数占位符，且是预期的查询格式
        if (!sql.matches("^SELECT \\* FROM \\w+ WHERE data_id = \\?$")) {
            throw new IllegalArgumentException("Invalid SQL statement format");
        }

        // 再次验证dataId的安全性
        if (!isValidDataId(dataId)) {
            throw new IllegalArgumentException("Invalid data ID format: " + dataId);
        }

        try {
            // 使用预编译语句执行查询
            return aggregateJdbcTemplate.queryForMap(sql, dataId);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute secure query: " + e.getMessage(), e);
        }
    }

    /**
     * 根据证书类型获取目标表查询SQL
     * 添加输入验证以防止SQL注入
     */
    private String getTargetTableSql(String certificateType) {
        // 输入验证：检查证书类型是否在允许的白名单中
        if (certificateType == null || !ALLOWED_CERTIFICATE_TYPES.contains(certificateType)) {
            throw new IllegalArgumentException("Invalid certificate type: " + certificateType);
        }

        switch(certificateType) {
            case "不参加航行和轮机值班海船船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_bcjhljzb WHERE data_id = ?";
            case "船员适任证书申请表":
                return "SELECT * FROM dwdb_ctf_cert_dtl_cysrzsqb WHERE data_id = ?";
            case "公务船船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_gwccy WHERE data_id = ?";
            case "海船高级船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_hcgjcy WHERE data_id = ?";
            case "海船普通船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_hcptcysrz WHERE data_id = ?";
            case "海船船员培训合格证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_hcpxhg WHERE data_id = ?";
            case "海上非自航船舶船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_hsfcysrz WHERE data_id = ?";
            case "海员外派机构资质证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_hywpjg WHERE data_id = ?";
            case "海船船员健康证明":
                return "SELECT * FROM dwdb_ctf_cert_dtl_jkzm WHERE data_id = ?";
            case "内河船舶船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_nhcbcy WHERE data_id = ?";
            case "海船船员内河航线行驶资格证明":
                return "SELECT * FROM dwdb_ctf_cert_dtl_nhhxxs WHERE data_id = ?";
            case "内河船舶船员培训合格证":
                return "SELECT * FROM dwdb_ctf_cert_dtl_nhpxhg WHERE data_id = ?";
            case "船员培训质量管理体系证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_qms WHERE data_id = ?";
            case "海船船员培训许可证":
                return "SELECT * FROM dwdb_ctf_cert_dtl_sea_per WHERE data_id = ?";
            case "特定航线江海直达船舶船员行驶资格证明培训合格证":
                return "SELECT * FROM dwdb_ctf_cert_dtl_tdhxjh WHERE data_id = ?";
            case "小型海船适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_xhcsrz WHERE data_id = ?";
            case "引航员船员适任证书":
                return "SELECT * FROM dwdb_ctf_cert_dtl_yhysrz WHERE data_id = ?";
            default:
                // 这个分支理论上不会被执行，因为已经在上面进行了白名单验证
                throw new IllegalArgumentException("Unsupported certificate type: " + certificateType);
        }
    }

    /**
     * 测试SQL注入防护功能
     */
    @Test
    public void testSqlInjectionPrevention() {
        // 测试无效的证书类型
        assertThrows(IllegalArgumentException.class, () -> {
            getTargetTableSql("'; DROP TABLE dwdb_ctf_cert_dtl_bcjhljzb; --");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            getTargetTableSql(null);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            getTargetTableSql("INVALID_CERTIFICATE_TYPE");
        });

        // 测试无效的数据ID
        assertFalse(isValidDataId("'; DROP TABLE test; --"));
        assertFalse(isValidDataId("1' OR '1'='1"));
        assertFalse(isValidDataId("test; DELETE FROM users"));
        assertFalse(isValidDataId(null));
        assertFalse(isValidDataId(""));
        assertFalse(isValidDataId("   "));

        // 测试有效的数据ID
        assertTrue(isValidDataId("valid-data-id-123"));
        assertTrue(isValidDataId("ABC123"));
        assertTrue(isValidDataId("test_data_456"));

        // 测试executeSecureQuery方法的安全性
        // 测试无效的SQL格式
        assertThrows(IllegalArgumentException.class, () -> {
            executeSecureQuery("SELECT * FROM users; DROP TABLE test;", "valid-id");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            executeSecureQuery("SELECT * FROM users WHERE id = ? OR 1=1", "valid-id");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            executeSecureQuery(null, "valid-id");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            executeSecureQuery("", "valid-id");
        });

        // 测试无效的数据ID
        assertThrows(IllegalArgumentException.class, () -> {
            executeSecureQuery("SELECT * FROM dwdb_ctf_cert_dtl_bcjhljzb WHERE data_id = ?", "'; DROP TABLE test; --");
        });
    }

    /**
     * 比较源表和目标表的字段值
     */
    private void assertFieldsEqual(Map<String, Object> source, Map<String, Object> target, String certificateType) {
        // 通用字段比较
        assertEquals(source.get("dataid"), target.get("data_id"));
        assertEquals(source.get("certificatetype"), target.get("certificate_type"));
        assertEquals(source.get("certificatetypecode"), target.get("certificate_type_code"));
        assertEquals(source.get("issuedept"), target.get("certificate_issuing_authority_name"));
        assertEquals(source.get("issuedeptcode"), target.get("certificate_issuing_authority_code"));
        assertEquals(source.get("certificateareacode"), target.get("certificate_area_code"));
        assertEquals(source.get("certificateholder"), target.get("certificate_holder_name"));
        assertEquals(source.get("certificateholdercode"), target.get("certificate_holder_code"));
        assertEquals(source.get("certificateholdertype"), target.get("certificate_holder_type_name"));
        assertEquals(source.get("issuedate"), target.get("certificate_issued_date"));
        assertEquals(source.get("validbegindate"), target.get("certificate_effective_date"));
        assertEquals(source.get("validenddate"), target.get("certificate_expiring_date"));
        assertEquals(source.get("status"), target.get("certificate_status"));
        assertEquals(source.get("creator"), target.get("creator_id"));
        assertEquals(source.get("createtime"), target.get("create_time"));
        assertEquals(source.get("operator"), target.get("operator_id"));
        assertEquals(source.get("updatetime"), target.get("update_time"));
        assertEquals(source.get("filepath"), target.get("file_path"));
        assertEquals(source.get("syncstatus"), target.get("sync_status"));
        assertEquals(source.get("remarks"), target.get("remarks"));
        assertEquals(source.get("deptid"), target.get("dept_id"));
        assertEquals(source.get("applynum"), target.get("apply_num"));
        assertEquals(source.get("affairtype"), target.get("affair_type"));
        assertEquals(source.get("servebusiness"), target.get("serve_business"));
        assertEquals(source.get("affairid"), target.get("affair_id"));
        assertEquals(source.get("affairnum"), target.get("affair_num"));
        assertEquals(source.get("qztype"), target.get("qz_type"));
        assertEquals(source.get("drafturl"), target.get("draft_url"));
        assertEquals(source.get("sortname"), target.get("sort_name"));
        assertEquals(source.get("sealname"), target.get("sealname"));
        
        // 根据证书类型比较特定字段
        switch(certificateType) {
                case "不参加航行和轮机值班海船船员适任证书":
                assertBcjhljzbFields(source, target);
                    break;
            case "海船高级船员适任证书":
                assertHcgjcyFields(source, target);
                    break;
            case "海船船员培训合格证书":
                assertHcpxhgFields(source, target);
                    break;
            case "海上非自航船舶船员适任证书":
                assertHsfhcysrzFields(source, target);
                    break;
            case "海船船员培训许可证":
                assertSeamanPermitFields(source, target);
                    break;
            case "船员适任证书申请表":
                assertCysrzsqbFields(source, target);
                    break;
                
            case "公务船船员适任证书":
                assertGwccyFields(source, target);
                    break;
                
            case "海船普通船员适任证书":
                assertHcptcysrzFields(source, target);
                    break;
                
                case "海船船员健康证明":
                assertJkzmFields(source, target);
                    break;
                
                case "内河船舶船员适任证书":
                assertNhcbcyFields(source, target);
                    break;
                
                case "海船船员内河航线行驶资格证明":
                assertNhhxxsFields(source, target);
                    break;
                
                case "内河船舶船员培训合格证":
                assertNhpxhgFields(source, target);
                    break;
                
                case "船员培训质量管理体系证书":
                assertQmsFields(source, target);
                    break;
                
                case "特定航线江海直达船舶船员行驶资格证明培训合格证":
                assertTdhxjhFields(source, target);
                    break;
                
                case "小型海船适任证书":
                assertXhcsrzFields(source, target);
                    break;
                
                case "引航员船员适任证书":
                assertYhysrzFields(source, target);
                    break;
        }
    }

    private void assertBcjhljzbFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的字段列表
            Set<String> validFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",
                "nationality1", "nationality2",
                "dateOfBirth1", "dateOfBirth2",
                "gender1", "gender2",
                "certificateNo",
                "certificateExpiringDate1", "certificateExpiringDate2",
                "dateOfIssue1", "dateOfIssue2",
                "certificateHolderName",
                "informationOfPhoto",
                "capacity1", "capacity2", "capacity3", "capacity4",
                "applivations1", "applivations2", "applivations3", "applivations4",
                "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
                "issuingAuthority1", "issuingAuthority2",
                "officialUseOnly1", "officialUseOnly2"
            ));
            
            // 检查JSON中的所有字段是否都是合法的
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                if(!validFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            assertEquals(surfaceJson.get("capacity1").asText(), target.get("capacity1"));
            assertEquals(surfaceJson.get("capacity2").asText(), target.get("capacity2"));
            assertEquals(surfaceJson.get("capacity3").asText(), target.get("capacity3"));
            assertEquals(surfaceJson.get("capacity4").asText(), target.get("capacity4"));
            assertEquals(surfaceJson.get("applivations1").asText(), target.get("applivations1"));
            assertEquals(surfaceJson.get("applivations2").asText(), target.get("applivations2"));
            assertEquals(surfaceJson.get("applivations3").asText(), target.get("applivations3"));
            assertEquals(surfaceJson.get("applivations4").asText(), target.get("applivations4"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial1").asText(), target.get("name_of_duly_authorized_official1"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial2").asText(), target.get("name_of_duly_authorized_official2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("officialUseOnly1").asText(), target.get("official_use_only1"));
            assertEquals(surfaceJson.get("officialUseOnly2").asText(), target.get("official_use_only2"));
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertHcgjcyFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "certificateNo", "nationality", "dateOfBirth", "gender",
                "certificateExpiringDate", "dateOfIssue", "certificateHolderName",
                "informationOfPhoto", "nameOfDulyAuthorizedOfficial",
                "issuingAuthority", "officialUseOnly"
            ));
            
            // 定义合法的能力子表字段前缀
            Set<String> validCapacityPrefixes = new HashSet<>(Arrays.asList(
                "Jobs", "ship_name", "ship_type", "gross_tonnage", "main_propulsion_power",
                "trading_area", "limitations", "date_of_endorsement", "date_of_expiry"
            ));
            
            // 定义合法的职能子表字段前缀
            Set<String> validFunctionPrefixes = new HashSet<>(Arrays.asList(
                "function", "level", "limitations_applying"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(validMainFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是能力子表字段
                boolean isValidCapacityField = false;
                for(String prefix : validCapacityPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidCapacityField = true;
                    break;
            }
        }
                if(isValidCapacityField) {
                    continue;
                }
                
                // 检查是否是职能子表字段
                boolean isValidFunctionField = false;
                for(String prefix : validFunctionPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidFunctionField = true;
                        break;
                    }
                }
                if(isValidFunctionField) {
                    continue;
                }
                
                // 如果都不是合法字段，则失败
                fail("发现非法字段: " + fieldName);
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("nationality").asText(), target.get("nationality1")); 
            assertEquals(surfaceJson.get("nationality").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender").asText(), target.get("gender2"));
            assertEquals(surfaceJson.get("certificateExpiringDate").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial").asText(), target.get("name_of_duly_authorized_official1"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial").asText(), target.get("name_of_duly_authorized_official2"));
            assertEquals(surfaceJson.get("issuingAuthority").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("officialUseOnly").asText(), target.get("official_use_only1"));
            assertEquals(surfaceJson.get("officialUseOnly").asText(), target.get("official_use_only2"));
            
            // 验证能力子表
            String capacitySql = "SELECT * FROM dwdb_ctf_cert_dtl_hcgjcy_cap WHERE hcgjcy_id = ?";
            List<Map<String, Object>> capacityList = aggregateJdbcTemplate.queryForList(capacitySql, target.get("hcgjcy_id"));
            assertFalse(capacityList.isEmpty());
            
            // 根据字段前缀和序号提取能力记录
            Map<Integer, Map<String, String>> capacityRecords = new HashMap<>();
            for(String prefix : validCapacityPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        capacityRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix, surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较能力记录
            assertEquals(capacityRecords.size(), capacityList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : capacityRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = capacityList.get(entry.getKey() - 1);
                
                for(String prefix : validCapacityPrefixes) {
                    if(record.containsKey(prefix)) {
                        assertEquals(record.get(prefix), dbRecord.get(prefix));
                    }
                }
            }
            
            // 验证职能子表 (类似处理)
            String functionSql = "SELECT * FROM dwdb_ctf_cert_dtl_hcgjcy_func WHERE hcgjcy_id = ?";
            List<Map<String, Object>> functionList = aggregateJdbcTemplate.queryForList(functionSql, target.get("hcgjcy_id"));
            assertFalse(functionList.isEmpty());
            
            Map<Integer, Map<String, String>> functionRecords = new HashMap<>();
            for(String prefix : validFunctionPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        functionRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix, surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            assertEquals(functionRecords.size(), functionList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : functionRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = functionList.get(entry.getKey() - 1);
                
                for(String prefix : validFunctionPrefixes) {
                    if(record.containsKey(prefix)) {
                        assertEquals(record.get(prefix), dbRecord.get(prefix));
                    }
                }
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertHcpxhgFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "name", "sex", "number", "printNo", "photo",
                "idNumber", "nationality", "dateOfBirth", "placeOfBirth",
                "address", "phoneNumber", "email",
                "issuingAuthority", "nameOfDulyAuthorizedOfficial",
                "year", "month", "day",
                "certificateStatus", "certificateStatusDate", "certificateStatusReason",
                "certificateStatusDept", "certificateStatusOfficial", "remarks"
            ));
            
            // 定义合法的培训子表字段前缀
            Set<String> validTrainingPrefixes = new HashSet<>(Arrays.asList(
                "Jobs", "ship_name", "training_type", "training_content",
                "training_institution", "training_start_date", "training_end_date",
                "training_result", "training_certificate_no"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(validMainFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是培训子表字段
                boolean isValidTrainingField = false;
                for(String prefix : validTrainingPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidTrainingField = true;
                        break;
                    }
                }
                if(isValidTrainingField) {
                    continue;
                }
                
                // 如果都不是合法字段，则失败
                fail("发现非法字段: " + fieldName);
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("name").asText(), target.get("name"));
            assertEquals(surfaceJson.get("sex").asText(), target.get("sex"));
            assertEquals(surfaceJson.get("number").asText(), target.get("number"));
            assertEquals(surfaceJson.get("printNo").asText(), target.get("print_no"));
            assertEquals(surfaceJson.get("photo").asText(), target.get("photo"));
            assertEquals(surfaceJson.get("idNumber").asText(), target.get("id_number"));
            assertEquals(surfaceJson.get("nationality").asText(), target.get("nationality"));
            assertEquals(surfaceJson.get("dateOfBirth").asText(), target.get("date_of_birth"));
            assertEquals(surfaceJson.get("placeOfBirth").asText(), target.get("place_of_birth"));
            assertEquals(surfaceJson.get("address").asText(), target.get("address"));
            assertEquals(surfaceJson.get("phoneNumber").asText(), target.get("phone_number"));
            assertEquals(surfaceJson.get("email").asText(), target.get("email"));
            assertEquals(surfaceJson.get("issuingAuthority").asText(), target.get("issuing_authority"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial").asText(), target.get("name_of_duly_authorized_official"));
            assertEquals(surfaceJson.get("year").asText(), target.get("year"));
            assertEquals(surfaceJson.get("month").asText(), target.get("month"));
            assertEquals(surfaceJson.get("day").asText(), target.get("day"));
            assertEquals(surfaceJson.get("certificateStatus").asText(), target.get("certificate_status"));
            assertEquals(surfaceJson.get("certificateStatusDate").asText(), target.get("certificate_status_date"));
            assertEquals(surfaceJson.get("certificateStatusReason").asText(), target.get("certificate_status_reason"));
            assertEquals(surfaceJson.get("certificateStatusDept").asText(), target.get("certificate_status_dept"));
            assertEquals(surfaceJson.get("certificateStatusOfficial").asText(), target.get("certificate_status_official"));
            assertEquals(surfaceJson.get("remarks").asText(), target.get("remarks"));
            
            // 验证培训子表
            String trainingSql = "SELECT * FROM dwdb_ctf_cert_dtl_hcpxhg_train WHERE hcpxhg_id = ?";
            List<Map<String, Object>> trainingList = aggregateJdbcTemplate.queryForList(trainingSql, target.get("hcpxhg_id"));
            assertFalse(trainingList.isEmpty());
            
            // 根据字段前缀和序号提取培训记录
            Map<Integer, Map<String, String>> trainingRecords = new HashMap<>();
            for(String prefix : validTrainingPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        trainingRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix.replaceAll("1[1-9]$", ""), surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较培训记录
            assertEquals(trainingRecords.size(), trainingList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : trainingRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = trainingList.get(entry.getKey() - 1);
                
                for(String prefix : validTrainingPrefixes) {
                    String fieldName = prefix.replaceAll("1[1-9]$", "");
                    if(record.containsKey(fieldName)) {
                        assertEquals(record.get(fieldName), dbRecord.get(fieldName));
                    }
                }
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertHsfhcysrzFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",
                "nationality1", "nationality2",
                "dateOfBirth1", "dateOfBirth2",
                "gender1", "gender2",
                "certificateNo",
                "certificateExpiringDate1", "certificateExpiringDate2",
                "dateOfIssue1", "dateOfIssue2",
                "certificateHolderName",
                "informationOfPhoto",
                "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
                "issuingAuthority1", "issuingAuthority2",
                "officialUseOnly1", "officialUseOnly2"
            ));
            
            // 定义合法的船舶子表字段前缀
            Set<String> validShipPrefixes = new HashSet<>(Arrays.asList(
                "Jobs", "ship_name", "ship_type", "gross_tonnage", 
                "main_propulsion_power", "trading_area", "limitations",
                "date_of_endorsement", "date_of_expiry"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(validMainFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是船舶子表字段
                boolean isValidShipField = false;
                for(String prefix : validShipPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidShipField = true;
                        break;
                    }
                }
                if(isValidShipField) {
                    continue;
                }
                
                // 如果都不是合法字段，则失败
                fail("发现非法字段: " + fieldName);
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial1").asText(), target.get("name_of_duly_authorized_official1"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial2").asText(), target.get("name_of_duly_authorized_official2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("officialUseOnly1").asText(), target.get("official_use_only1"));
            assertEquals(surfaceJson.get("officialUseOnly2").asText(), target.get("official_use_only2"));
            
            // 验证船舶子表
            String shipSql = "SELECT * FROM dwdb_ctf_cert_dtl_hsfcysrz_ship WHERE hsfhcysrz_id = ?";
            List<Map<String, Object>> shipList = aggregateJdbcTemplate.queryForList(shipSql, target.get("hsfhcysrz_id"));
            assertFalse(shipList.isEmpty());
            
            // 根据字段前缀和序号提取船舶记录
            Map<Integer, Map<String, String>> shipRecords = new HashMap<>();
            for(String prefix : validShipPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        shipRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix.replaceAll("1[1-9]$", ""), surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较船舶记录
            assertEquals(shipRecords.size(), shipList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : shipRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = shipList.get(entry.getKey() - 1);
                
                for(String prefix : validShipPrefixes) {
                    String fieldName = prefix.replaceAll("1[1-9]$", "");
                    if(record.containsKey(fieldName)) {
                        assertEquals(record.get(fieldName), dbRecord.get(fieldName));
                    }
                }
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertSeamanPermitFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "permitNumber1", "anThorityName1", "trainingInstitutionCode1",
                "representative1", "trainingProgram1", "trainingProgram2",
                "registeredAddress1", "trainingLocation1", "periodOfValidity1",
                "periodOfValidity2", "issuingAuthority1", "dateofIssue1",
                "permitNumber2", "anThorityName2", "registeredAddress2",
                "representative2", "trainingLocation2", "periodOfValidity3",
                "periodOfValidity4", "remarks", "issuingAuthority2", "dateofIssue2"
            ));
            
            // 定义合法的机构信息表字段列表
            Set<String> validInfoFields = new HashSet<>(Arrays.asList(
                "organizationName", "organizationCode", "creditCode",
                "legalRepresentative", "address", "phoneNumber",
                "email", "scope", "validityPeriod",
                "number", "nameCn", "nameEn", "sexCn", "sexEn",
                "countryCn", "countryEn", "birthCn", "birthEn",
                "fileNoCn", "fileNoEn", "qualificationCn", "qualificationEn",
                "initialDateCn", "initialDateEn", "expiryDateCn", "expiryDateEn",
                "signDeptCn", "signDeptEn", "officeOfIssueCn", "officeOfIssueEn",
                "date", "photo", "year", "month", "day"
            ));
            
            // 定义合法的培训项目字段前缀
            Set<String> validItemPrefixes = new HashSet<>(Arrays.asList(
                "number", "atrainingProgram", "trainingScale"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(validMainFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是机构信息表字段
                if(validInfoFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是培训项目字段
                boolean isValidItemField = false;
                for(String prefix : validItemPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidItemField = true;
                        break;
                    }
                }
                if(isValidItemField) {
                    continue;
                }
                
                // 如果都不是合法字段，则失败
                fail("发现非法字段: " + fieldName);
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("permitNumber1").asText(), target.get("permit_number1"));
            assertEquals(surfaceJson.get("anThorityName1").asText(), target.get("an_thority_name1"));
            assertEquals(surfaceJson.get("trainingInstitutionCode1").asText(), target.get("training_institution_code1"));
            assertEquals(surfaceJson.get("representative1").asText(), target.get("representative1"));
            assertEquals(surfaceJson.get("trainingProgram1").asText(), target.get("training_program1"));
            assertEquals(surfaceJson.get("trainingProgram2").asText(), target.get("training_program2"));
            assertEquals(surfaceJson.get("registeredAddress1").asText(), target.get("registered_address1"));
            assertEquals(surfaceJson.get("trainingLocation1").asText(), target.get("training_location1"));
            assertEquals(surfaceJson.get("periodOfValidity1").asText(), target.get("period_of_validity1"));
            assertEquals(surfaceJson.get("periodOfValidity2").asText(), target.get("period_of_validity2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("dateofIssue1").asText(), target.get("dateof_issue1"));
            assertEquals(surfaceJson.get("permitNumber2").asText(), target.get("permit_number2"));
            assertEquals(surfaceJson.get("anThorityName2").asText(), target.get("an_thority_name2"));
            assertEquals(surfaceJson.get("registeredAddress2").asText(), target.get("registered_address2"));
            assertEquals(surfaceJson.get("representative2").asText(), target.get("representative2"));
            assertEquals(surfaceJson.get("trainingLocation2").asText(), target.get("training_location2"));
            assertEquals(surfaceJson.get("periodOfValidity3").asText(), target.get("period_of_validity3"));
            assertEquals(surfaceJson.get("periodOfValidity4").asText(), target.get("period_of_validity4"));
            assertEquals(surfaceJson.get("remarks").asText(), target.get("remarks"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("dateofIssue2").asText(), target.get("dateof_issue2"));
            
            // 验证机构信息表
            String infoSql = "SELECT * FROM dwdb_ctf_cert_dtl_seaman_info WHERE seaman_permit_id = ?";
            Map<String, Object> infoRecord = aggregateJdbcTemplate.queryForMap(infoSql, target.get("seaman_permit_id"));
            
            // 基本信息
            assertEquals(surfaceJson.get("number").asText(), infoRecord.get("number"));
            assertEquals(surfaceJson.get("nameCn").asText(), infoRecord.get("name_cn"));
            assertEquals(surfaceJson.get("nameEn").asText(), infoRecord.get("name_en"));
            assertEquals(surfaceJson.get("sexCn").asText(), infoRecord.get("sex_cn"));
            assertEquals(surfaceJson.get("sexEn").asText(), infoRecord.get("sex_en"));
            assertEquals(surfaceJson.get("countryCn").asText(), infoRecord.get("country_cn"));
            assertEquals(surfaceJson.get("countryEn").asText(), infoRecord.get("country_en"));
            assertEquals(surfaceJson.get("birthCn").asText(), infoRecord.get("birth_cn"));
            assertEquals(surfaceJson.get("birthEn").asText(), infoRecord.get("birth_en"));
            assertEquals(surfaceJson.get("fileNoCn").asText(), infoRecord.get("file_no_cn"));
            assertEquals(surfaceJson.get("fileNoEn").asText(), infoRecord.get("file_no_en"));
            assertEquals(surfaceJson.get("qualificationCn").asText(), infoRecord.get("qualification_cn"));
            assertEquals(surfaceJson.get("qualificationEn").asText(), infoRecord.get("qualification_en"));
            assertEquals(surfaceJson.get("initialDateCn").asText(), infoRecord.get("initial_date_cn"));
            assertEquals(surfaceJson.get("initialDateEn").asText(), infoRecord.get("initial_date_en"));
            assertEquals(surfaceJson.get("expiryDateCn").asText(), infoRecord.get("expiry_date_cn"));
            assertEquals(surfaceJson.get("expiryDateEn").asText(), infoRecord.get("expiry_date_en"));
            assertEquals(surfaceJson.get("signDeptCn").asText(), infoRecord.get("sign_dept_cn"));
            assertEquals(surfaceJson.get("signDeptEn").asText(), infoRecord.get("sign_dept_en"));
            assertEquals(surfaceJson.get("officeOfIssueCn").asText(), infoRecord.get("office_of_issue_cn"));
            assertEquals(surfaceJson.get("officeOfIssueEn").asText(), infoRecord.get("office_of_issue_en"));
            assertEquals(surfaceJson.get("date").asText(), infoRecord.get("date"));
            assertEquals(surfaceJson.get("photo").asText(), infoRecord.get("photo"));
            assertEquals(surfaceJson.get("year").asText(), infoRecord.get("year"));
            assertEquals(surfaceJson.get("month").asText(), infoRecord.get("month"));
            assertEquals(surfaceJson.get("day").asText(), infoRecord.get("day"));
            
            // 验证培训项目表
            String itemSql = "SELECT * FROM dwdb_ctf_cert_dtl_sea_per_item WHERE seaman_permit_id = ?";
            List<Map<String, Object>> itemList = aggregateJdbcTemplate.queryForList(itemSql, target.get("seaman_permit_id"));
            assertFalse(itemList.isEmpty());
            
            // 根据字段前缀和序号提取培训项目记录
            Map<Integer, Map<String, String>> itemRecords = new HashMap<>();
            for(String prefix : validItemPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        itemRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix.replaceAll("1[1-9]$", ""), surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较培训项目记录
            assertEquals(itemRecords.size(), itemList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : itemRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = itemList.get(entry.getKey() - 1);
                
                for(String prefix : validItemPrefixes) {
                    String fieldName = prefix.replaceAll("1[1-9]$", "");
                    if(record.containsKey(fieldName)) {
                        assertEquals(record.get(fieldName), dbRecord.get(fieldName));
                    }
                }
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertGwccyFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",
                "nationality1", "nationality2",
                "dateOfBirth1", "dateOfBirth2",
                "gender1", "gender2",
                "certificateNo",
                "certificateExpiringDate1", "certificateExpiringDate2",
                "dateOfIssue1", "dateOfIssue2",
                "certificateHolderName",
                "informationOfPhoto",
                "gradwAndCapacity1", "gradwAndCapacity2",
                "limiationsApplying1", "limiationsApplying2",
                "issuingAuthority1", "issuingAuthority2",
                "officialUseOnly1"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            assertEquals(surfaceJson.get("gradwAndCapacity1").asText(), target.get("gradw_and_capacity1"));
            assertEquals(surfaceJson.get("gradwAndCapacity2").asText(), target.get("gradw_and_capacity2"));
            assertEquals(surfaceJson.get("limiationsApplying1").asText(), target.get("limiations_applying1"));
            assertEquals(surfaceJson.get("limiationsApplying2").asText(), target.get("limiations_applying2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("officialUseOnly1").asText(), target.get("official_use_only1"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertJkzmFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",
                "nationality1", "nationality2",
                "dateOfBirth1", "dateOfBirth2",
                "gender1", "gender2",
                "department1", "department2",
                "certificateNo",
                "certificateExpiringDate1", "certificateExpiringDate2",
                "dateOfIssue1", "dateOfIssue2",
                "certificateHolderName",
                "informationOfPhoto",
                "yesOrNo1", "yesOrNo2", "yesOrNo3", "yesOrNo4", "yesOrNo5",
                "yesOrNo6", "yesOrNo7", "yesOrNo8", "yesOrNo9",
                "authorizingAuthority1", "authorizingAuthority2",
                "doctorName1", "doctorName2",
                "issuingAuthority1", "issuingAuthority2"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            // 基本信息(中英文)
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            assertEquals(surfaceJson.get("department1").asText(), target.get("department1"));
            assertEquals(surfaceJson.get("department2").asText(), target.get("department2"));
            
            // 证书信息
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            
            // 体检选项
            for(int i = 1; i <= 9; i++) {
                assertEquals(surfaceJson.get("yesOrNo" + i).asText(), target.get("yes_or_no" + i));
            }
            
            // 签发信息(中英文)
            assertEquals(surfaceJson.get("authorizingAuthority1").asText(), target.get("authorizing_authority1"));
            assertEquals(surfaceJson.get("authorizingAuthority2").asText(), target.get("authorizing_authority2"));
            assertEquals(surfaceJson.get("doctorName1").asText(), target.get("doctor_name1"));
            assertEquals(surfaceJson.get("doctorName2").asText(), target.get("doctor_name2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
                
            } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertNhcbcyFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "name",          // 姓名
                "sex",           // 性别
                "number",        // 证件号码
                "type",          // 船员类型
                "endDate",       // 有效期限
                "signDept",      // 签发部门
                "printNo",       // 印刷号码
                "scope",         // 适用范围
                "photo",         // 照片信息
                "year",          // 签发年份
                "month",         // 签发月份
                "day",           // 签发日期
                "issueDept",     // 发证机关
                "signDate"       // 签发日期
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("name").asText(), target.get("name"));
            assertEquals(surfaceJson.get("sex").asText(), target.get("sex"));
            assertEquals(surfaceJson.get("number").asText(), target.get("number"));
            assertEquals(surfaceJson.get("type").asText(), target.get("type"));
            assertEquals(surfaceJson.get("endDate").asText(), target.get("end_date"));
            assertEquals(surfaceJson.get("signDept").asText(), target.get("sign_dept"));
            assertEquals(surfaceJson.get("printNo").asText(), target.get("print_no"));
            assertEquals(surfaceJson.get("scope").asText(), target.get("scope"));
            assertEquals(surfaceJson.get("photo").asText(), target.get("photo"));
            assertEquals(surfaceJson.get("year").asText(), target.get("year"));
            assertEquals(surfaceJson.get("month").asText(), target.get("month"));
            assertEquals(surfaceJson.get("day").asText(), target.get("day"));
            assertEquals(surfaceJson.get("issueDept").asText(), target.get("issue_dept"));
            assertEquals(surfaceJson.get("signDate").asText(), target.get("sign_date"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertNhhxxsFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "numberOfCertificate",  // 证书编号
                "name",                 // 姓名
                "gender",               // 性别
                "creditCode",           // 身份证号
                "dateOfIssue",          // 签发日期
                "expiryDate",           // 到期日期
                "issuingDate",          // 发证日期
                "applivations",         // 适用范围说明
                "photo",                // 照片信息
                "issuingAuthority"      // 发证机关
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("numberOfCertificate").asText(), target.get("number_of_certificate"));
            assertEquals(surfaceJson.get("name").asText(), target.get("name"));
            assertEquals(surfaceJson.get("gender").asText(), target.get("gender"));
            assertEquals(surfaceJson.get("creditCode").asText(), target.get("credit_code"));
            assertEquals(surfaceJson.get("dateOfIssue").asText(), target.get("date_of_issue"));
            assertEquals(surfaceJson.get("expiryDate").asText(), target.get("expiry_date"));
            assertEquals(surfaceJson.get("issuingDate").asText(), target.get("issuing_date"));
            assertEquals(surfaceJson.get("applivations").asText(), target.get("applivations"));
            assertEquals(surfaceJson.get("photo").asText(), target.get("photo"));
            assertEquals(surfaceJson.get("issuingAuthority").asText(), target.get("issuing_authority"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertNhpxhgFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "name",      // 姓名
                "sex",       // 性别
                "number",    // 证件号码
                "printNo",   // 印刷号码
                "photo",     // 照片信息
                "year",      // 签发年份
                "month",     // 签发月份
                "day"        // 签发日期
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("name").asText(), target.get("name"));
            assertEquals(surfaceJson.get("sex").asText(), target.get("sex"));
            assertEquals(surfaceJson.get("number").asText(), target.get("number"));
            assertEquals(surfaceJson.get("printNo").asText(), target.get("print_no"));
            assertEquals(surfaceJson.get("photo").asText(), target.get("photo"));
            assertEquals(surfaceJson.get("year").asText(), target.get("year"));
            assertEquals(surfaceJson.get("month").asText(), target.get("month"));
            assertEquals(surfaceJson.get("day").asText(), target.get("day"));
            
            // 验证子表数据
            String itemSql = "SELECT * FROM dwdb_ctf_cert_dtl_nhpxhg_item WHERE nhpxhg_id = ?";
            List<Map<String, Object>> itemList = aggregateJdbcTemplate.queryForList(itemSql, target.get("nhpxhg_id"));
            
            // 从JSON中提取子表记录
            Map<Integer, Map<String, String>> itemRecords = new HashMap<>();
            for(int i = 1; i <= 9; i++) {
                String project = getStringValue(surfaceJson, "project1" + i);
                String signDept = getStringValue(surfaceJson, "signDept1" + i);
                String signDate = getStringValue(surfaceJson, "signDate1" + i);
                String endDate = getStringValue(surfaceJson, "endDate1" + i);
                
                // 只要有一个字段存在值就创建记录
                if(!StringUtils.isAllEmpty(project, signDept, signDate, endDate)) {
                    Map<String, String> record = new HashMap<>();
                    record.put("project", project);
                    record.put("sign_dept", signDept);
                    record.put("sign_date", signDate);
                    record.put("end_date", endDate);
                    itemRecords.put(i, record);
                }
            }
            
            // 比较子表记录数
            assertEquals(itemRecords.size(), itemList.size(), "子表记录数不匹配");
            
            // 比较子表记录内容
            for(int i = 0; i < itemList.size(); i++) {
                Map<String, Object> dbRecord = itemList.get(i);
                Map<String, String> jsonRecord = itemRecords.get(i + 1);
                
                assertEquals(jsonRecord.get("project"), dbRecord.get("project"));
                assertEquals(jsonRecord.get("sign_dept"), dbRecord.get("sign_dept"));
                assertEquals(jsonRecord.get("sign_date"), dbRecord.get("sign_date"));
                assertEquals(jsonRecord.get("end_date"), dbRecord.get("end_date"));
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private String getStringValue(JsonNode json, String field) {
        JsonNode node = json.get(field);
        return node != null ? node.asText() : "";
    }

    private void assertQmsFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "number1",                           // 证书编号1
                "fullNameOfTheHolder1",              // 持证人中文名称
                "fullNameOfTheHolder2",              // 持证人英文名称
                "year1",                            // 有效期年份
                "month1",                           // 有效期月份
                "day1",                             // 有效期日期
                "certificateExpiringDate",          // 证书到期日期(英文格式)
                "nameOfDulyAuthorizedOfficial1",    // 授权官员姓名
                "evaluationOrganization1",          // 评估机构中文名称
                "evaluationOrganization2",          // 评估机构英文名称
                "dateOfIssue1",                     // 签发日期(中文格式)
                "dateOfIssue2",                     // 签发日期(英文格式)
                "number2",                          // 证书编号2
                "remarks"                           // 备注
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            // 证书编号
            assertEquals(surfaceJson.get("number1").asText(), target.get("number1"));
            assertEquals(surfaceJson.get("number2").asText(), target.get("number2"));
            
            // 持证人名称(中英文)
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            
            // 有效期信息
            assertEquals(surfaceJson.get("year1").asText(), target.get("year1"));
            assertEquals(surfaceJson.get("month1").asText(), target.get("month1"));
            assertEquals(surfaceJson.get("day1").asText(), target.get("day1"));
            assertEquals(surfaceJson.get("certificateExpiringDate").asText(), target.get("certificate_expiring_date"));
            
            // 评估机构(中英文)
            assertEquals(surfaceJson.get("evaluationOrganization1").asText(), target.get("evaluation_organization1"));
            assertEquals(surfaceJson.get("evaluationOrganization2").asText(), target.get("evaluation_organization2"));
            
            // 签发信息
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial1").asText(), target.get("name_of_duly_authorized_official1"));
            
            // 其他信息
            assertEquals(surfaceJson.get("remarks").asText(), target.get("remarks"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertTdhxjhFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "numberOfCertificate",  // 证书编号
                "name",                 // 姓名
                "dateOfBirth",          // 出生日期
                "creditCode",           // 身份证号
                "gender",               // 性别
                "dateOfIssue",          // 签发日期
                "expiryDate",           // 有效期限
                "applivations",         // 特定航线范围
                "limitationsApplying",  // 限制说明
                "photo",                // 照片信息
                "issuingAuthority",     // 发证机关
                "issuingDate"           // 发证日期
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("numberOfCertificate").asText(), target.get("number_of_certificate"));
            assertEquals(surfaceJson.get("name").asText(), target.get("name"));
            assertEquals(surfaceJson.get("dateOfBirth").asText(), target.get("date_of_birth"));
            assertEquals(surfaceJson.get("creditCode").asText(), target.get("credit_code"));
            assertEquals(surfaceJson.get("gender").asText(), target.get("gender"));
            assertEquals(surfaceJson.get("dateOfIssue").asText(), target.get("date_of_issue"));
            assertEquals(surfaceJson.get("expiryDate").asText(), target.get("expiry_date"));
            assertEquals(surfaceJson.get("applivations").asText(), target.get("applivations"));
            assertEquals(surfaceJson.get("limitationsApplying").asText(), target.get("limitations_applying"));
            assertEquals(surfaceJson.get("photo").asText(), target.get("photo"));
            assertEquals(surfaceJson.get("issuingAuthority").asText(), target.get("issuing_authority"));
            assertEquals(surfaceJson.get("issuingDate").asText(), target.get("issuing_date"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertXhcsrzFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",      // 持证人姓名(中英文)
                "nationality1", "nationality2",                       // 国籍(中英文)
                "dateOfBirth1", "dateOfBirth2",                      // 出生日期(中英文)
                "gender1", "gender2",                                // 性别(中英文)
                "certificateNo",                                     // 证书编号
                "certificateExpiringDate1", "certificateExpiringDate2", // 证书到期日期(中英文)
                "dateOfIssue1", "dateOfIssue2",                     // 签发日期(中英文)
                "certificateHolderName",                            // 持证人姓名
                "articleNumber1", "articleNumber2",                  // 条款编号(中文)
                "articleNumber3", "articleNumber4",                  // 条款编号(英文)
                "informationOfPhoto",                               // 照片信息
                "function1", "function2",                           // 职能备注
                "level1", "level2",                                 // 等级(中英文)
                "limitationsApplying1", "limitationsApplying2",     // 适用限制(中英文)
                "gradwAndCapacity1", "gradwAndCapacity2",           // 职务等级(中英文)
                "alimitationsApplying1", "alimitationsApplying2",   // 职务限制(中英文)
                "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2", // 授权官员姓名(中英文)
                "issuingAuthority1", "issuingAuthority2",           // 发证机关(中英文)
                "officialUseOnly1"                                  // 官方使用
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            // 基本信息(中英文)
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            
            // 证书信息
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            
            // 条款信息
            assertEquals(surfaceJson.get("articleNumber1").asText(), target.get("article_number1"));
            assertEquals(surfaceJson.get("articleNumber2").asText(), target.get("article_number2"));
            assertEquals(surfaceJson.get("articleNumber3").asText(), target.get("article_number3"));
            assertEquals(surfaceJson.get("articleNumber4").asText(), target.get("article_number4"));
            
            // 照片信息
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            
            // 职能和等级信息
            assertEquals(surfaceJson.get("function1").asText(), target.get("function1"));
            assertEquals(surfaceJson.get("function2").asText(), target.get("function2"));
            assertEquals(surfaceJson.get("level1").asText(), target.get("level1"));
            assertEquals(surfaceJson.get("level2").asText(), target.get("level2"));
            
            // 限制和职务信息
            assertEquals(surfaceJson.get("limitationsApplying1").asText(), target.get("limitations_applying1"));
            assertEquals(surfaceJson.get("limitationsApplying2").asText(), target.get("limitations_applying2"));
            assertEquals(surfaceJson.get("gradwAndCapacity1").asText(), target.get("gradw_and_capacity1"));
            assertEquals(surfaceJson.get("gradwAndCapacity2").asText(), target.get("gradw_and_capacity2"));
            assertEquals(surfaceJson.get("alimitationsApplying1").asText(), target.get("alimitations_applying1"));
            assertEquals(surfaceJson.get("alimitationsApplying2").asText(), target.get("alimitations_applying2"));
            
            // 签发信息
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial1").asText(), target.get("name_of_duly_authorized_official1"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial2").asText(), target.get("name_of_duly_authorized_official2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("officialUseOnly1").asText(), target.get("official_use_only1"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertYhysrzFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",      // 持证人姓名(中英文)
                "nationality1", "nationality2",                       // 国籍/省份(中英文)
                "dateOfBirth1", "dateOfBirth2",                      // 出生日期(中英文)
                "gender1", "gender2",                                // 性别(中英文)
                "certificateNo",                                     // 证书编号
                "certificateExpiringDate1", "certificateExpiringDate2", // 证书到期日期(中英文)
                "dateOfIssue1", "dateOfIssue2",                     // 签发日期(中英文)
                "certificateHolderName",                            // 持证人姓名
                "informationOfPhoto",                               // 照片信息
                "type1", "type2",                                   // 引航员类型(中英文)
                "level1", "level2",                                 // 等级(中英文)
                "pilotageArea1", "pilotageArea2",                   // 引航区域(中英文)
                "limitationOfPolotage1", "limitationOfPolotage2",   // 引航限制(中英文)
                "remarks",                                          // 备注
                "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2", // 授权官员姓名(中英文)
                "issuingAuthority1", "issuingAuthority2"            // 发证机关(中英文)
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(!validMainFields.contains(fieldName)) {
                    fail("发现非法字段: " + fieldName);
                }
            }
            
            // 主表字段比较
            // 基本信息(中英文)
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            
            // 证书信息
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            
            // 引航员信息(中英文)
            assertEquals(surfaceJson.get("type1").asText(), target.get("type1"));
            assertEquals(surfaceJson.get("type2").asText(), target.get("type2"));
            assertEquals(surfaceJson.get("level1").asText(), target.get("level1"));
            assertEquals(surfaceJson.get("level2").asText(), target.get("level2"));
            assertEquals(surfaceJson.get("pilotageArea1").asText(), target.get("pilotage_area1"));
            assertEquals(surfaceJson.get("pilotageArea2").asText(), target.get("pilotage_area2"));
            assertEquals(surfaceJson.get("limitationOfPolotage1").asText(), target.get("limitation_of_polotage1"));
            assertEquals(surfaceJson.get("limitationOfPolotage2").asText(), target.get("limitation_of_polotage2"));
            
            // 其他信息
            assertEquals(surfaceJson.get("remarks").asText(), target.get("remarks"));
            
            // 签发信息(中英文)
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial1").asText(), target.get("name_of_duly_authorized_official1"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial2").asText(), target.get("name_of_duly_authorized_official2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertCysrzsqbFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "number", "name", "gender", "nationality", "dateOfBirth",
                "placeOfBirth", "address", "idNumber", "phoneNumber",
                "email", "photo", "certificateType", "certificateGrade",
                "shipType", "position", "trainingInstitution",
                "trainingStartDate", "trainingEndDate", "examDate",
                "examPlace", "examResult"
            ));
            
            // 定义合法的经历子表字段前缀
            Set<String> validExperiencePrefixes = new HashSet<>(Arrays.asList(
                "ship_name", "ship_type", "position", "start_date",
                "end_date", "voyage_area", "total_days"
            ));
            
            // 定义合法的选项子表字段前缀
            Set<String> validOptionPrefixes = new HashSet<>(Arrays.asList(
                "option_key", "option_value", "option_type", "remarks"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(validMainFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是经历子表字段
                boolean isValidExperienceField = false;
                for(String prefix : validExperiencePrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidExperienceField = true;
                        break;
                    }
                }
                if(isValidExperienceField) {
                    continue;
                }
                
                // 检查是否是选项子表字段
                boolean isValidOptionField = false;
                for(String prefix : validOptionPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidOptionField = true;
                        break;
                    }
                }
                if(isValidOptionField) {
                    continue;
                }
                
                // 如果都不是合法字段，则失败
                fail("发现非法字段: " + fieldName);
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("number").asText(), target.get("number"));
            assertEquals(surfaceJson.get("name").asText(), target.get("name"));
            assertEquals(surfaceJson.get("gender").asText(), target.get("gender"));
            assertEquals(surfaceJson.get("nationality").asText(), target.get("nationality"));
            assertEquals(surfaceJson.get("dateOfBirth").asText(), target.get("date_of_birth"));
            assertEquals(surfaceJson.get("placeOfBirth").asText(), target.get("place_of_birth"));
            assertEquals(surfaceJson.get("address").asText(), target.get("address"));
            assertEquals(surfaceJson.get("idNumber").asText(), target.get("id_number"));
            assertEquals(surfaceJson.get("phoneNumber").asText(), target.get("phone_number"));
            assertEquals(surfaceJson.get("email").asText(), target.get("email"));
            assertEquals(surfaceJson.get("photo").asText(), target.get("photo"));
            assertEquals(surfaceJson.get("certificateType").asText(), target.get("certificate_type"));
            assertEquals(surfaceJson.get("certificateGrade").asText(), target.get("certificate_grade"));
            assertEquals(surfaceJson.get("shipType").asText(), target.get("ship_type"));
            assertEquals(surfaceJson.get("position").asText(), target.get("position"));
            assertEquals(surfaceJson.get("trainingInstitution").asText(), target.get("training_institution"));
            assertEquals(surfaceJson.get("trainingStartDate").asText(), target.get("training_start_date"));
            assertEquals(surfaceJson.get("trainingEndDate").asText(), target.get("training_end_date"));
            assertEquals(surfaceJson.get("examDate").asText(), target.get("exam_date"));
            assertEquals(surfaceJson.get("examPlace").asText(), target.get("exam_place"));
            assertEquals(surfaceJson.get("examResult").asText(), target.get("exam_result"));
            
            // 验证经历子表
            String experienceSql = "SELECT * FROM dwdb_ctf_cert_dtl_cysrzsqb_exp WHERE cysrzsqb_id = ?";
            List<Map<String, Object>> experienceList = aggregateJdbcTemplate.queryForList(experienceSql, target.get("cysrzsqb_id"));
            assertFalse(experienceList.isEmpty());
            
            // 根据字段前缀和序号提取经历记录
            Map<Integer, Map<String, String>> experienceRecords = new HashMap<>();
            for(String prefix : validExperiencePrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        experienceRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix.replaceAll("1[1-9]$", ""), surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较经历记录
            assertEquals(experienceRecords.size(), experienceList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : experienceRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = experienceList.get(entry.getKey() - 1);
                
                for(String prefix : validExperiencePrefixes) {
                    String fieldName = prefix.replaceAll("1[1-9]$", "");
                    if(record.containsKey(fieldName)) {
                        assertEquals(record.get(fieldName), dbRecord.get(fieldName));
                    }
                }
            }
            
            // 验证选项子表
            String optionsSql = "SELECT * FROM dwdb_ctf_cert_dtl_cysrzsqb_ops WHERE cysrzsqb_id = ?";
            List<Map<String, Object>> optionsList = aggregateJdbcTemplate.queryForList(optionsSql, target.get("cysrzsqb_id"));
            assertFalse(optionsList.isEmpty());
            
            // 根据字段前缀和序号提取选项记录
            Map<Integer, Map<String, String>> optionRecords = new HashMap<>();
            for(String prefix : validOptionPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        optionRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix.replaceAll("1[1-9]$", ""), surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较选项记录
            assertEquals(optionRecords.size(), optionsList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : optionRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = optionsList.get(entry.getKey() - 1);
                
                for(String prefix : validOptionPrefixes) {
                    String fieldName = prefix.replaceAll("1[1-9]$", "");
                    if(record.containsKey(fieldName)) {
                        assertEquals(record.get(fieldName), dbRecord.get(fieldName));
                    }
                }
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    private void assertHcptcysrzFields(Map<String, Object> source, Map<String, Object> target) {
        try {
            String surfaceData = (String)source.get("surfacedata");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode surfaceJson = mapper.readTree(surfaceData);
            
            // 定义合法的主表字段列表
            Set<String> validMainFields = new HashSet<>(Arrays.asList(
                "fullNameOfTheHolder1", "fullNameOfTheHolder2",
                "nationality1", "nationality2",
                "dateOfBirth1", "dateOfBirth2",
                "gender1", "gender2",
                "certificateNo",
                "certificateExpiringDate1", "certificateExpiringDate2",
                "dateOfIssue1", "dateOfIssue2",
                "certificateHolderName",
                "informationOfPhoto",
                "nameOfDulyAuthorizedOfficial1", "nameOfDulyAuthorizedOfficial2",
                "issuingAuthority1", "issuingAuthority2",
                "officialUseOnly1", "officialUseOnly2"
            ));
            
            // 定义合法的能力子表字段前缀
            Set<String> validCapacityPrefixes = new HashSet<>(Arrays.asList(
                "gradw_and_capacity", "alimitations_applying"
            ));
            
            // 检查JSON中的所有字段是否合法
            Iterator<String> fieldNames = surfaceJson.fieldNames();
            while(fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                
                // 检查是否是主表字段
                if(validMainFields.contains(fieldName)) {
                    continue;
                }
                
                // 检查是否是能力子表字段
                boolean isValidCapacityField = false;
                for(String prefix : validCapacityPrefixes) {
                    if(fieldName.matches(prefix + "1[1-9]")) {
                        isValidCapacityField = true;
                        break;
                    }
                }
                if(isValidCapacityField) {
                    continue;
                }
                
                // 如果都不是合法字段，则失败
                fail("发现非法字段: " + fieldName);
            }
            
            // 主表字段比较
            assertEquals(surfaceJson.get("fullNameOfTheHolder1").asText(), target.get("full_name_of_the_holder1"));
            assertEquals(surfaceJson.get("fullNameOfTheHolder2").asText(), target.get("full_name_of_the_holder2"));
            assertEquals(surfaceJson.get("nationality1").asText(), target.get("nationality1"));
            assertEquals(surfaceJson.get("nationality2").asText(), target.get("nationality2"));
            assertEquals(surfaceJson.get("dateOfBirth1").asText(), target.get("date_of_birth1"));
            assertEquals(surfaceJson.get("dateOfBirth2").asText(), target.get("date_of_birth2"));
            assertEquals(surfaceJson.get("gender1").asText(), target.get("gender1"));
            assertEquals(surfaceJson.get("gender2").asText(), target.get("gender2"));
            assertEquals(surfaceJson.get("certificateNo").asText(), target.get("certificate_no"));
            assertEquals(surfaceJson.get("certificateExpiringDate1").asText(), target.get("certificate_expiring_date1"));
            assertEquals(surfaceJson.get("certificateExpiringDate2").asText(), target.get("certificate_expiring_date2"));
            assertEquals(surfaceJson.get("dateOfIssue1").asText(), target.get("date_of_issue1"));
            assertEquals(surfaceJson.get("dateOfIssue2").asText(), target.get("date_of_issue2"));
            assertEquals(surfaceJson.get("certificateHolderName").asText(), target.get("certificate_holder_name"));
            assertEquals(surfaceJson.get("informationOfPhoto").asText(), target.get("information_of_photo"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial1").asText(), target.get("name_of_duly_authorized_official1"));
            assertEquals(surfaceJson.get("nameOfDulyAuthorizedOfficial2").asText(), target.get("name_of_duly_authorized_official2"));
            assertEquals(surfaceJson.get("issuingAuthority1").asText(), target.get("issuing_authority1"));
            assertEquals(surfaceJson.get("issuingAuthority2").asText(), target.get("issuing_authority2"));
            assertEquals(surfaceJson.get("officialUseOnly1").asText(), target.get("official_use_only1"));
            assertEquals(surfaceJson.get("officialUseOnly2").asText(), target.get("official_use_only2"));
            
            // 验证能力子表
            String capacitySql = "SELECT * FROM dwdb_ctf_cert_dtl_hcptcysrz_cap WHERE hcptcysrz_id = ?";
            List<Map<String, Object>> capacityList = aggregateJdbcTemplate.queryForList(capacitySql, target.get("hcptcysrz_id"));
            assertFalse(capacityList.isEmpty());
            
            // 根据字段前缀和序号提取能力记录
            Map<Integer, Map<String, String>> capacityRecords = new HashMap<>();
            for(String prefix : validCapacityPrefixes) {
                for(int i = 1; i <= 9; i++) {
                    String fieldName = prefix + "1" + i;
                    if(surfaceJson.has(fieldName)) {
                        capacityRecords.computeIfAbsent(i, k -> new HashMap<>())
                            .put(prefix.replaceAll("1[1-9]$", ""), surfaceJson.get(fieldName).asText());
                    }
                }
            }
            
            // 比较能力记录
            assertEquals(capacityRecords.size(), capacityList.size());
            for(Map.Entry<Integer, Map<String, String>> entry : capacityRecords.entrySet()) {
                Map<String, String> record = entry.getValue();
                Map<String, Object> dbRecord = capacityList.get(entry.getKey() - 1);
                
                for(String prefix : validCapacityPrefixes) {
                    String fieldName = prefix.replaceAll("1[1-9]$", "");
                    if(record.containsKey(fieldName)) {
                        assertEquals(record.get(fieldName), dbRecord.get(fieldName));
                    }
                }
            }
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }
} 