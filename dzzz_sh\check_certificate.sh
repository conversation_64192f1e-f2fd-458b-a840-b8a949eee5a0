#!/bin/bash

# 检查参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <certificate_number>"
    exit 1
fi

CERTIFICATE_NUMBER=$1

# 数据库连接信息
DB_HOST="localhost"
DB_PORT="54321"
DB_NAME="your_database"
DB_USER="your_username"
DB_PASSWORD="your_password"

# 首先查询证书类型
CERT_TYPE_QUERY="
SELECT certificate_name 
FROM dwdb_certificate_data 
WHERE certificate_number = '$CERTIFICATE_NUMBER';"

# 获取证书类型
CERT_TYPE=$(ksql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -W $DB_PASSWORD -t -A -c "$CERT_TYPE_QUERY")

# 根据证书类型执行不同的查询
if [ "$CERT_TYPE" = "海船船员培训合格证书" ]; then
    # 海船船员培训合格证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    train_data AS (
        SELECT 
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'prefix', ht.prefix,
                    'title_of_the_certificate1', ht.title_of_the_certificate1,
                    'title_of_the_certificate2', ht.title_of_the_certificate2,
                    'level', ht.level,
                    'date_of_issue1', ht.date_of_issue1,
                    'date_of_issue2', ht.date_of_issue2,
                    'date_of_expiry1', ht.date_of_expiry1,
                    'date_of_expiry2', ht.date_of_expiry2
                )
            ) as train_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_hcpxhg h ON ci.data_id = h.data_id
        JOIN dwdb_ctf_cert_dtl_hcpxhg_train ht ON h.hcpxhg_id = ht.hcpxhg_id
        GROUP BY ci.data_id
    )
    SELECT 
        cd.certificate_name,
        h.full_name_of_the_holder1,
        h.full_name_of_the_holder2,
        h.nationality1,
        h.nationality2,
        h.date_of_birth1,
        h.date_of_birth2,
        h.gender1,
        h.gender2,
        h.certificate_no,
        h.issued_on1,
        h.issued_on2,
        h.information_of_photo,
        h.offical_seal1,
        h.offical_seal2,
        h.official_use_only1,
        h.official_use_only2,
        h.name_of_duly_aut_offi1,
        h.name_of_duly_aut_offi2,
        attr1.attribute_value as official1,
        attr2.attribute_value as official2,
        t.train_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_hcpxhg h ON cd.data_id = h.data_id
    LEFT JOIN dwdb_certificate_data_attribute attr1 ON cd.data_id = attr1.data_id AND attr1.attribute_column_name = 'official1'
    LEFT JOIN dwdb_certificate_data_attribute attr2 ON cd.data_id = attr2.data_id AND attr2.attribute_column_name = 'official2'
    LEFT JOIN train_data t ON 1=1
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "内河船舶船员培训合格证" ]; then
    # 内河船舶船员培训合格证查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    item_data AS (
        SELECT 
            n.nhpxhg_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'project_name', i.project_name,
                    'sign_dept', i.sign_dept,
                    'sign_date', i.sign_date,
                    'end_date', i.end_date
                )
            ) as item_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_nhpxhg n ON ci.data_id = n.data_id
        JOIN dwdb_ctf_cert_dtl_nhpxhg_item i ON n.nhpxhg_id = i.nhpxhg_id
        GROUP BY n.nhpxhg_id
    )
    SELECT 
        cd.certificate_name,
        cd.certificate_issuing_authority_name,
        cd.certificate_issued_date,
        n.name,
        n.sex,
        n.number,
        n.print_no,
        n.photo,
        i.item_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_nhpxhg n ON cd.data_id = n.data_id
    LEFT JOIN item_data i ON n.nhpxhg_id = i.nhpxhg_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "内河船舶船员适任证书" ]; then
    # 内河船舶船员适任证书查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        n.name,
        n.sex,
        n.number,
        n.type,
        n.end_date,
        n.sign_dept,
        n.print_no,
        n.scope,
        n.issue_dept,
        n.sign_date
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_nhcbsr n ON cd.data_id = n.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "游艇驾驶证（海上）" ] || [ "$CERT_TYPE" = "游艇驾驶证（内河）" ]; then
    # 游艇驾驶证查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        y.qualification_cn,
        y.qualification_en,
        y.full_name_of_the_holder1,
        y.full_name_of_the_holder2,
        y.nationality1,
        y.nationality2,
        y.date_of_birth1,
        y.date_of_birth2,
        y.gender1,
        y.gender2,
        y.certificate_no,
        y.date_of_expiry1,
        y.date_of_expiry2,
        y.issued_on1,
        y.issued_on2,
        y.information_of_photo,
        y.office_of_issue_cn,
        y.office_of_issue_en
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_ytjsz y ON cd.data_id = y.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海船船员内河航线行驶资格证明" ]; then
    # 海船船员内河航线行驶资格证明查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        n.name,
        n.gender,
        n.credit_code,
        n.number_of_certificate,
        n.date_of_issue,
        n.applivations,
        n.photo
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_nhhxxs n ON cd.data_id = n.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "内河船员培训许可证" ]; then
    # 内河船员培训许可证查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    item_data AS (
        SELECT 
            n.nhcyxkz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'number', i.number,
                    'atraining_program', i.atraining_program,
                    'training_scale', i.training_scale
                )
            ) as item_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_nhcyxkz n ON ci.data_id = n.data_id
        JOIN dwdb_ctf_cert_dtl_nhcyxkz_item i ON n.nhcyxkz_id = i.nhcyxkz_id
        GROUP BY n.nhcyxkz_id
    )
    SELECT 
        cd.certificate_name,
        n.permit_number1,
        n.an_thority_name1,
        n.training_institution_code1,
        n.representative1,
        n.training_program1,
        n.training_program2,
        n.registered_address1,
        n.training_location1,
        n.period_of_validity1,
        n.period_of_validity2,
        n.issuing_authority1,
        n.dateof_issue1,
        n.permit_number2,
        n.an_thority_name2,
        n.registered_address2,
        n.representative2,
        n.training_location2,
        n.period_of_validity3,
        n.period_of_validity4,
        n.remarks,
        n.issuing_authority2,
        n.dateof_issue2,
        i.item_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_nhcyxkz n ON cd.data_id = n.data_id
    LEFT JOIN item_data i ON n.nhcyxkz_id = i.nhcyxkz_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"
    
elif [ "$CERT_TYPE" = "海船船员健康证明" ]; then
    # 海船船员健康证明查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        j.full_name_of_the_holder1,
        j.full_name_of_the_holder2,
        j.nationality1,
        j.nationality2,
        j.date_of_birth1,
        j.date_of_birth2,
        j.gender1,
        j.gender2,
        j.department1,
        j.department2,
        j.certificate_no,
        j.certificate_expiring_date1,
        j.certificate_expiring_date2,
        j.date_of_issue1,
        j.date_of_issue2,
        j.information_of_photo,
        j.yes_or_no1,
        j.yes_or_no2,
        j.yes_or_no3,
        j.yes_or_no4,
        j.yes_or_no5,
        j.yes_or_no6,
        j.yes_or_no7,
        j.yes_or_no8,
        j.yes_or_no9,
        j.authorizing_authority1,
        j.authorizing_authority2,
        j.doctor_name1,
        j.doctor_name2,
        j.issuing_authority1,
        j.issuing_authority2
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_jkzm j ON cd.data_id = j.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海船普通船员适任证书" ]; then
    # 海船普通船员适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    cap_data AS (
        SELECT 
            h.hcptcysrz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'gradw_and_capacity1', c.gradw_and_capacity1,
                    'gradw_and_capacity2', c.gradw_and_capacity2,
                    'alimitations_applying1', c.alimitations_applying1,
                    'alimitations_applying2', c.alimitations_applying2
                )
            ) as cap_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_hcptcysrz h ON ci.data_id = h.data_id
        JOIN dwdb_ctf_cert_dtl_hcptcysrz_cap c ON h.hcptcysrz_id = c.hcptcysrz_id
        GROUP BY h.hcptcysrz_id
    ),
    fun_data AS (
        SELECT 
            h.hcptcysrz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'function1', f.function1,
                    'function2', f.function2,
                    'level1', f.level1,
                    'level2', f.level2,
                    'limitations_applying1', f.limitations_applying1,
                    'limitations_applying2', f.limitations_applying2
                )
            ) as fun_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_hcptcysrz h ON ci.data_id = h.data_id
        JOIN dwdb_ctf_cert_dtl_hcptcysrz_fun f ON h.hcptcysrz_id = f.hcptcysrz_id
        GROUP BY h.hcptcysrz_id
    )
    SELECT 
        cd.certificate_name,
        h.full_name_of_the_holder1,
        h.full_name_of_the_holder2,
        h.nationality1,
        h.nationality2,
        h.date_of_birth1,
        h.date_of_birth2,
        h.gender1,
        h.gender2,
        h.certificate_no,
        h.certificate_expiring_date1,
        h.certificate_expiring_date2,
        h.date_of_issue1,
        h.date_of_issue2,
        h.information_of_photo,
        h.name_of_duly_authorized_official1,
        h.name_of_duly_authorized_official2,
        h.issuing_authority1,
        h.issuing_authority2,
        h.official_use_only1,
        h.official_use_only2,
        attr1.attribute_value as capacity11,
        attr2.attribute_value as capacity12,
        c.cap_info,
        f.fun_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_hcptcysrz h ON cd.data_id = h.data_id
    LEFT JOIN dwdb_certificate_data_attribute attr1 ON cd.data_id = attr1.data_id AND attr1.attribute_column_name = 'capacity11'
    LEFT JOIN dwdb_certificate_data_attribute attr2 ON cd.data_id = attr2.data_id AND attr2.attribute_column_name = 'capacity12'
    LEFT JOIN cap_data c ON h.hcptcysrz_id = c.hcptcysrz_id
    LEFT JOIN fun_data f ON h.hcptcysrz_id = f.hcptcysrz_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "不参加航行和轮机值班海船船员适任证书" ]; then
    # 不参加航行和轮机值班海船船员适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    cap_data AS (
        SELECT 
            b.bcjhljzb_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'capacity1', c.capacity1,
                    'capacity2', c.capacity2,
                    'applivations1', c.applivations1,
                    'applivations2', c.applivations2
                )
            ) as cap_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_bcjhljzb b ON ci.data_id = b.data_id
        JOIN dwdb_ctf_cert_dtl_bcjhljzb_cap c ON b.bcjhljzb_id = c.bcjhljzb_id
        GROUP BY b.bcjhljzb_id
    )
    SELECT 
        cd.certificate_name,
        b.full_name_of_the_holder1,
        b.full_name_of_the_holder2,
        b.nationality1,
        b.nationality2,
        b.date_of_birth1,
        b.date_of_birth2,
        b.gender1,
        b.gender2,
        b.certificate_no,
        b.certificate_expiring_date1,
        b.certificate_expiring_date2,
        b.certificate_issued_date1,
        b.certificate_issued_date2,
        b.information_of_photo,
        b.name_of_duly_authorized_official1,
        b.name_of_duly_authorized_official2,
        b.issuing_authority1,
        b.issuing_authority2,
        b.official_use_only1,
        b.official_use_only2,
        c.cap_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_bcjhljzb b ON cd.data_id = b.data_id
    LEFT JOIN cap_data c ON b.bcjhljzb_id = c.bcjhljzb_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海船高级船员适任证书" ]; then
    # 海船高级船员适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    cap_data AS (
        SELECT 
            h.hcgjcy_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'gradw_and_capacity1', c.gradw_and_capacity1,
                    'gradw_and_capacity2', c.gradw_and_capacity2,
                    'alimitations_applying1', c.alimitations_applying1,
                    'alimitations_applying2', c.alimitations_applying2
                )
            ) as cap_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_hcgjcy h ON ci.data_id = h.data_id
        JOIN dwdb_ctf_cert_dtl_hcgjcy_cap c ON h.hcgjcy_id = c.hcgjcy_id
        GROUP BY h.hcgjcy_id
    ),
    func_data AS (
        SELECT 
            h.hcgjcy_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'function1', f.function1,
                    'function2', f.function2,
                    'level1', f.level1,
                    'level2', f.level2,
                    'limitations_applying1', f.limitations_applying1,
                    'limitations_applying2', f.limitations_applying2
                )
            ) as func_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_hcgjcy h ON ci.data_id = h.data_id
        JOIN dwdb_ctf_cert_dtl_hcgjcy_func f ON h.hcgjcy_id = f.hcgjcy_id
        GROUP BY h.hcgjcy_id
    )
    SELECT 
        cd.certificate_name,
        h.full_name_of_the_holder1,
        h.full_name_of_the_holder2,
        h.nationality1,
        h.nationality2,
        h.date_of_birth1,
        h.date_of_birth2,
        h.gender1,
        h.gender2,
        h.certificate_no,
        h.certificate_expiring_date1,
        h.certificate_expiring_date2,
        h.date_of_issue1,
        h.date_of_issue2,
        h.information_of_photo,
        h.article_number1,
        h.article_number2,
        h.article_number3,
        h.article_number4,
        h.name_of_duly_authorized_official1,
        h.name_of_duly_authorized_official2,
        h.issuing_authority1,
        h.issuing_authority2,
        h.official_use_only1,
        h.official_use_only2,
        c.cap_info,
        f.func_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_hcgjcy h ON cd.data_id = h.data_id
    LEFT JOIN cap_data c ON h.hcgjcy_id = c.hcgjcy_id
    LEFT JOIN func_data f ON h.hcgjcy_id = f.hcgjcy_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "船上厨师培训合格证明" ]; then
    # 船上厨师培训合格证明查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        c.full_name_of_the_holder1,
        c.full_name_of_the_holder2,
        c.nationality1,
        c.nationality2,
        c.date_of_birth1,
        c.date_of_birth2,
        c.gender1,
        c.gender2,
        c.certificate_no,
        c.date_of_issue1,
        c.date_of_issue2,
        c.information_of_photo,
        c.name_of_the_traing_manager1,
        c.name_of_the_traing_manager2,
        c.issuing_body1,
        c.issuing_body2
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_cscspx c ON cd.data_id = c.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "小型海船适任证书" ]; then
    # 小型海船适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    cap_data AS (
        SELECT 
            x.xhcsrz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'gradw_and_capacity1', c.gradw_and_capacity1,
                    'gradw_and_capacity2', c.gradw_and_capacity2,
                    'alimitations_applying1', c.alimitations_applying1,
                    'alimitations_applying2', c.alimitations_applying2
                )
            ) as cap_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_xhcsrz x ON ci.data_id = x.data_id
        JOIN dwdb_ctf_cert_dtl_xhcsrz_cap c ON x.xhcsrz_id = c.xhcsrz_id
        GROUP BY x.xhcsrz_id
    ),
    func_data AS (
        SELECT 
            x.xhcsrz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'function1', f.function1,
                    'function2', f.function2,
                    'level1', f.level1,
                    'level2', f.level2,
                    'limitations_applying1', f.limitations_applying1,
                    'limitations_applying2', f.limitations_applying2
                )
            ) as func_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_xhcsrz x ON ci.data_id = x.data_id
        JOIN dwdb_ctf_cert_dtl_xhcsrz_func f ON x.xhcsrz_id = f.xhcsrz_id
        GROUP BY x.xhcsrz_id
    )
    SELECT 
        cd.certificate_name,
        x.full_name_of_the_holder1,
        x.full_name_of_the_holder2,
        x.nationality1,
        x.nationality2,
        x.date_of_birth1,
        x.date_of_birth2,
        x.gender1,
        x.gender2,
        x.certificate_no,
        x.certificate_expiring_date1,
        x.certificate_expiring_date2,
        x.date_of_issue1,
        x.date_of_issue2,
        x.article_number1,
        x.article_number2,
        x.article_number3,
        x.article_number4,
        x.information_of_photo,
        x.name_of_duly_authorized_official1,
        x.name_of_duly_authorized_official2,
        x.official_use_only1,
        x.official_use_only2,
        x.issuing_authority1,
        x.issuing_authority2,
        c.cap_info,
        f.func_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_xhcsrz x ON cd.data_id = x.data_id
    LEFT JOIN cap_data c ON x.xhcsrz_id = c.xhcsrz_id
    LEFT JOIN func_data f ON x.xhcsrz_id = f.xhcsrz_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海上非自航船舶船员适任证书" ]; then
    # 海上非自航船舶船员适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    ship_data AS (
        SELECT 
            h.hsfhcysrz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'ship_type', s.ship_type,
                    'level', s.level,
                    'capacity', s.capacity
                )
            ) as ship_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_hsfcysrz h ON ci.data_id = h.data_id
        JOIN dwdb_ctf_cert_dtl_hsfcysrz_ship s ON h.hsfhcysrz_id = s.hsfhcysrz_id
        GROUP BY h.hsfhcysrz_id
    )
    SELECT 
        cd.certificate_name,
        h.certificate_no,
        h.full_name_of_the_holder,
        h.date_of_birth,
        h.place_of_birth,
        h.date_of_expirty,
        h.date_of_issue,
        h.name_of_duly_authorized_official,
        h.information_of_photo,
        h.remark,
        s.ship_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_hsfcysrz h ON cd.data_id = h.data_id
    LEFT JOIN ship_data s ON h.hsfhcysrz_id = s.hsfhcysrz_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "公务船船员适任证书" ]; then
    # 公务船船员适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    cap_data AS (
        SELECT 
            g.gwccy_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'gradw_and_capacity1', c.gradw_and_capacity1,
                    'gradw_and_capacity2', c.gradw_and_capacity2,
                    'alimitations_applying1', c.alimitations_applying1,
                    'alimitations_applying2', c.alimitations_applying2
                )
            ) as cap_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_gwccy g ON ci.data_id = g.data_id
        JOIN dwdb_ctf_cert_dtl_gwccy_cap c ON g.gwccy_id = c.gwccy_id
        GROUP BY g.gwccy_id
    )
    SELECT 
        cd.certificate_name,
        g.full_name_of_the_holder1,
        g.full_name_of_the_holder2,
        g.nationality1,
        g.nationality2,
        g.date_of_birth1,
        g.date_of_birth2,
        g.gender1,
        g.gender2,
        g.certificate_no,
        g.certificate_expiring_date1,
        g.certificate_expiring_date2,
        g.date_of_issue1,
        g.date_of_issue2,
        g.information_of_photo,
        g.issuing_authority1,
        g.issuing_authority2,
        g.official_use_only1,
        g.official_use_only2,
        c.cap_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_gwccy g ON cd.data_id = g.data_id
    LEFT JOIN cap_data c ON g.gwccy_id = c.gwccy_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "引航员船员适任证书" ]; then
    # 引航员船员适任证书查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    range_data AS (
        SELECT 
            y.yhysrz_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'type1', r.type1,
                    'type2', r.type2,
                    'level1', r.level1,
                    'level2', r.level2,
                    'pilotage_area1', r.pilotage_area1,
                    'pilotage_area2', r.pilotage_area2,
                    'limitation_of_polotage1', r.limitation_of_polotage1,
                    'limitation_of_polotage2', r.limitation_of_polotage2
                )
            ) as range_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_yhysrz y ON ci.data_id = y.data_id
        JOIN dwdb_ctf_cert_dtl_yhysrz_range r ON y.yhysrz_id = r.yhysrz_id
        GROUP BY y.yhysrz_id
    )
    SELECT 
        cd.certificate_name,
        y.full_name_of_the_holder1,
        y.full_name_of_the_holder2,
        y.nationality1,
        y.nationality2,
        y.date_of_birth1,
        y.date_of_birth2,
        y.gender1,
        y.gender2,
        y.certificate_no,
        y.certificate_expiring_date1,
        y.certificate_expiring_date2,
        y.date_of_issue1,
        y.date_of_issue2,
        y.information_of_photo,
        y.remarks,
        y.name_of_duly_authorized_official1,
        y.name_of_duly_authorized_official2,
        y.issuing_authority1,
        y.issuing_authority2,
        r.range_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_yhysrz y ON cd.data_id = y.data_id
    LEFT JOIN range_data r ON y.yhysrz_id = r.yhysrz_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "特定航线江海直达船舶船员行驶资格证明培训合格证" ]; then
    # 特定航线江海直达船舶船员行驶资格证明培训合格证查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        t.number_of_certificate,
        t.name,
        t.date_of_birth,
        t.credit_code,
        t.gender,
        t.date_of_issue,
        t.applivations,
        t.limitations_applying,
        t.expiry_date,
        t.photo,
        t.issuing_authority,
        t.issuing_date
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_tdhxjh t ON cd.data_id = t.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "船上膳食服务辅助人员培训证明" ]; then
    # 船上膳食服务辅助人员培训证明查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        c.full_name_of_the_holder1,
        c.full_name_of_the_holder2,
        c.nationality1,
        c.nationality2,
        c.date_of_birth1,
        c.date_of_birth2,
        c.gender1,
        c.gender2,
        c.certificate_no,
        c.date_of_issue1,
        c.date_of_issue2,
        c.information_of_photo,
        c.name_of_the_traing_manager1,
        c.name_of_the_traing_manager2,
        c.issuing_body1,
        c.issuing_body2
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_csssfzpx c ON cd.data_id = c.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海员外派机构资质证书" ]; then
    # 海员外派机构资质证书查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        h.permit_number1,
        h.permit_number2,
        h.an_thority_name1,
        h.an_thority_name2,
        h.an_thority_name3,
        h.an_thority_name4,
        h.address1,
        h.address2,
        h.address3,
        h.address4,
        h.representative1,
        h.representative2,
        h.representative3,
        h.representative4,
        h.expiry_date1,
        h.expiry_date2,
        h.date_of_issue1,
        h.date_of_issue3,
        h.issuing_authority1,
        h.issuing_authority2,
        h.issuing_authority3,
        h.issuing_authority4,
        h.remark1,
        h.remark2,
        h.annual_examination
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_hywpjg h ON cd.data_id = h.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海船船员培训许可证" ]; then
    # 海船船员培训许可证查询
    SQL_QUERY="
    WITH certificate_info AS (
        SELECT cd.data_id, cd.certificate_name
        FROM dwdb_certificate_data cd
        WHERE cd.certificate_number = '$CERTIFICATE_NUMBER'
    ),
    item_data AS (
        SELECT 
            s.seaman_permit_id,
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'number', i.number,
                    'atraining_program', i.atraining_program,
                    'training_scale', i.training_scale
                )
            ) as item_info
        FROM certificate_info ci
        JOIN dwdb_ctf_cert_dtl_sea_per s ON ci.data_id = s.data_id
        JOIN dwdb_ctf_cert_dtl_sea_per_item i ON s.seaman_permit_id = i.seaman_permit_id
        GROUP BY s.seaman_permit_id
    )
    SELECT 
        cd.certificate_name,
        s.permit_number1,
        s.an_thority_name1,
        s.training_institution_code1,
        s.representative1,
        s.training_program1,
        s.training_program2,
        s.registered_address1,
        s.training_location1,
        s.period_of_validity1,
        s.period_of_validity2,
        s.issuing_authority1,
        s.dateof_issue1,
        s.permit_number2,
        s.an_thority_name2,
        s.registered_address2,
        s.representative2,
        s.training_location2,
        s.period_of_validity3,
        s.period_of_validity4,
        s.remarks,
        s.issuing_authority2,
        s.dateof_issue2,
        i.item_info
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_sea_per s ON cd.data_id = s.data_id
    LEFT JOIN item_data i ON s.seaman_permit_id = i.seaman_permit_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "船员培训质量管理体系证书" ]; then
    # 船员培训质量管理体系证书查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        q.number1,
        q.full_name_of_the_holder1,
        q.full_name_of_the_holder2,
        q.year1,
        q.month1,
        q.day1,
        q.certificate_expiring_date,
        q.evaluation_organization1,
        q.evaluation_organization2,
        q.date_of_issue1,
        q.date_of_issue2,
        q.number2
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_qms q ON cd.data_id = q.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

elif [ "$CERT_TYPE" = "海上设施工作人员海上交通安全技能培训合格证明" ]; then
    # 海上设施工作人员海上交通安全技能培训合格证明查询
    SQL_QUERY="
    SELECT 
        cd.certificate_name,
        h.full_name_of_the_holder1,
        h.full_name_of_the_holder2,
        h.edit_box1,
        h.nationality1,
        h.nationality2,
        h.date_of_birth1,
        h.date_of_birth2,
        h.gender1,
        h.gender2,
        h.certificate_no,
        h.an_thority_name1,
        h.an_thority_name2,
        h.evaluation_organization1,
        h.information_of_photo,
        h.year1,
        h.month1,
        h.day1,
        h.year2,
        h.month2,
        h.day2
    FROM dwdb_certificate_data cd
    LEFT JOIN dwdb_ctf_cert_dtl_hsssjn h ON cd.data_id = h.data_id
    WHERE cd.certificate_number = '$CERTIFICATE_NUMBER';"

else
    echo "未知的证书类型：$CERT_TYPE"
    exit 1
fi

# 执行SQL查询并格式化输出
ksql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -W $DB_PASSWORD -t -A -F "=" -c "$SQL_QUERY" | while IFS='=' read -r key value
do
    echo "$key=$value"
done 