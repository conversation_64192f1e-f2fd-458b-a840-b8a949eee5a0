#!/bin/bash

# 检查参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <certificate_number>"
    exit 1
fi

CERTIFICATE_NUMBER=$1

# 数据库连接信息
DB_HOST="localhost"
DB_PORT="54321"
DB_NAME="your_database"
DB_USER="your_username"
DB_PASSWORD="your_password"

# SQL查询
SQL_QUERY="
SELECT surfacedata
FROM ctf_certificate_data_regen
WHERE certificate_number = '$CERTIFICATE_NUMBER';"

# 执行SQL查询并获取JSON数据
JSON_DATA=$(ksql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -W $DB_PASSWORD -t -A -c "$SQL_QUERY")

# 检查是否获取到数据
if [ -z "$JSON_DATA" ]; then
    echo "未找到证书数据"
    exit 1
fi

# 使用jq解析JSON数组并格式化输出
echo "$JSON_DATA" | jq -r '.[] | "Name=\(.name)\nValue=\(.value)\n----------------------"' 