我来帮你创建一个详细的README.md文件，说明这两个脚本的功能和使用方法。

# 证书查询工具

本工具包含两个脚本用于查询证书信息:

## 1. check_certificate.sh

### 功能说明
该脚本用于根据证书编号查询证书详细信息。支持多种类型的证书查询，包括:

- 海船船员培训合格证书
- 内河船舶船员培训合格证
- 内河船舶船员适任证书
- 游艇驾驶证（海上/内河）
- 海船船员内河航线行驶资格证明
- 内河船员培训许可证
- 海船船员健康证明
- 海船普通船员适任证书
- 不参加航行和轮机值班海船船员适任证书
- 海船高级船员适任证书
- 船上厨师培训合格证明
- 小型海船适任证书
- 海上非自航船舶船员适任证书
- 公务船船员适任证书
- 引航员船员适任证书
- 特定航线江海直达船舶船员行驶资格证明培训合格证
- 船上膳食服务辅助人员培训证明
- 海员外派机构资质证书
- 海船船员培训许可证
- 船员培训质量管理体系证书
- 海上设施工作人员海上交通安全技能培训合格证明

### 使用方法
```bash
./check_certificate.sh <证书编号>
```

### 输出格式
脚本会根据证书类型输出相应的字段信息，格式为:
```
字段名1=字段值1
字段名2=字段值2
...
```

### 环境要求
- 人大金仓数据库
- ksql命令行工具
- 麒麟V10操作系统

## 2. parse_certificate.sh

### 功能说明
该脚本用于从ctf_certificate_data_regen表中解析证书的surface数据，以JSON格式展示证书信息。

### 使用方法
```bash
./parse_certificate.sh <证书编号>
```

### 输出格式
脚本会解析JSON数据并按以下格式输出:
```
Name=名称1
Value=值1
----------------------
Name=名称2
Value=值2
----------------------
```

### 环境要求
- 人大金仓数据库
- ksql命令行工具
- jq工具(用于JSON解析)
- 麒麟V10操作系统

## 3. sync_certificate_data.sh

### 功能说明
该脚本用于比对两个数据库中的表数据，并将源表(ods_certificate_data)中有而目标表(ctf_certificate_data_regen)中没有的数据同步到目标表中。脚本会按照以下步骤进行操作：

1. 按天统计源表和目标表的数据量并比对
2. 对于数量不一致的日期，按小时统计并比对
3. 对于数量不一致的小时，查询出源表中有而目标表中没有的记录
4. 将这些记录同步到目标表中

### 使用方法
```bash
./sync_certificate_data.sh <起始日期> <结束日期>
```

参数格式：YYYY-MM-DD，例如：2023-01-01

### 输出格式
脚本会生成日志文件，记录同步过程中的详细信息，包括：
- 按天统计的数据量比对结果
- 按小时统计的数据量比对结果
- 缺失记录的详细信息
- 同步操作的成功/失败状态

### 环境要求
- 人大金仓数据库
- ksql命令行工具
- 麒麟V10操作系统

## 数据库配置

所有脚本都需要配置数据库连接信息:

```bash
DB_HOST="localhost"
DB_PORT="54321"
DB_NAME="your_database"
DB_USER="your_username"
DB_PASSWORD="your_password"
```

对于sync_certificate_data.sh，需要配置源数据库和目标数据库的连接信息：

```bash
# 源数据库（正孚电子证照库）
SRC_DB_HOST="localhost"
SRC_DB_PORT="54321"
SRC_DB_NAME="source_database"
SRC_DB_USER="source_username"
SRC_DB_PASSWORD="source_password"

# 目标数据库（海事子平台业务库）
DEST_DB_HOST="localhost"
DEST_DB_PORT="54321"
DEST_DB_NAME="target_database"
DEST_DB_USER="target_username"
DEST_DB_PASSWORD="target_password"
```

请根据实际环境修改这些配置参数。

## 注意事项

1. 使用前请确保有足够的数据库访问权限
2. 证书编号必须是有效的
3. 建议在使用前先测试数据库连接是否正常
4. 脚本执行需要相应的执行权限，可通过以下命令授权：
```bash
chmod +x check_certificate.sh parse_certificate.sh sync_certificate_data.sh
```
5. 对于sync_certificate_data.sh，建议在非业务高峰期执行同步操作
6. 同步前建议对目标数据库进行备份

## 错误处理

- 如果未提供证书编号，脚本会显示使用方法并退出
- 如果查询不到证书信息，会提示"未找到证书数据"
- 对于未知的证书类型，check_certificate.sh会显示"未知的证书类型"并退出
- 对于sync_certificate_data.sh，如果日期格式不正确或起始日期大于结束日期，会显示错误信息并退出

## 示例

1. 查询证书详细信息：
```bash
./check_certificate.sh CERT2023001
```

2. 解析证书surface数据：
```bash
./parse_certificate.sh CERT2023001
```

3. 同步指定日期范围的数据：
```bash
./sync_certificate_data.sh 2023-01-01 2023-01-31
```
此命令将比对2023年1月1日至1月31日期间的数据，并同步缺失记录。
