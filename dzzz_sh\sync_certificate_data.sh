#!/bin/bash

# 数据同步比对工具
# 用于比对两个数据库中的表数据，并将源表中有而目标表中没有的数据同步到目标表中

# 日志文件
LOG_FILE="sync_certificate_data_$(date +%Y%m%d%H%M%S).log"

# 函数：写入日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 函数：检查参数
check_params() {
    if [ $# -ne 2 ]; then
        log "用法: $0 <起始日期> <结束日期>"
        log "日期格式: YYYY-MM-DD"
        exit 1
    fi

    # 验证日期格式
    if ! [[ $1 =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]] || ! [[ $2 =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
        log "错误: 日期格式不正确，应为YYYY-MM-DD"
        exit 1
    fi

    # 验证起始日期不大于结束日期
    if [[ $(date -d "$1" +%s) -gt $(date -d "$2" +%s) ]]; then
        log "错误: 起始日期不能大于结束日期"
        exit 1
    }
}

# 数据库连接信息
# 源数据库（正孚电子证照库）
SRC_DB_HOST="localhost"
SRC_DB_PORT="54321"
SRC_DB_NAME="source_database"
SRC_DB_USER="source_username"
SRC_DB_PASSWORD="source_password"

# 目标数据库（海事子平台业务库）
DEST_DB_HOST="localhost"
DEST_DB_PORT="54321"
DEST_DB_NAME="target_database"
DEST_DB_USER="target_username"
DEST_DB_PASSWORD="target_password"

# 表名
SRC_TABLE="ods_certificate_data"
DEST_TABLE="ctf_certificate_data_regen"

# 检查参数
check_params "$@"

START_DATE="$1"
END_DATE="$2"

log "开始执行数据同步比对，时间范围: $START_DATE 至 $END_DATE"

# 生成日期序列
generate_date_sequence() {
    local start_date="$1"
    local end_date="$2"
    local current_date="$start_date"
    
    while [[ $(date -d "$current_date" +%s) -le $(date -d "$end_date" +%s) ]]; do
        echo "$current_date"
        current_date=$(date -d "$current_date + 1 day" +%Y-%m-%d)
    done
}

# 按天统计源表数据量
count_src_by_day() {
    local check_date="$1"
    local query="
    SELECT COUNT(*) 
    FROM $SRC_TABLE 
    WHERE TO_CHAR(TO_TIMESTAMP(createtime, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') = '$check_date';"
    
    ksql -h $SRC_DB_HOST -p $SRC_DB_PORT -U $SRC_DB_USER -d $SRC_DB_NAME -W $SRC_DB_PASSWORD -t -A -c "$query"
}

# 按天统计目标表数据量
count_dest_by_day() {
    local check_date="$1"
    local query="
    SELECT COUNT(*) 
    FROM $DEST_TABLE 
    WHERE TO_CHAR(TO_TIMESTAMP(createtime, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD') = '$check_date';"
    
    ksql -h $DEST_DB_HOST -p $DEST_DB_PORT -U $DEST_DB_USER -d $DEST_DB_NAME -W $DEST_DB_PASSWORD -t -A -c "$query"
}

# 按小时统计源表数据量
count_src_by_hour() {
    local check_date="$1"
    local check_hour="$2"
    local query="
    SELECT COUNT(*) 
    FROM $SRC_TABLE 
    WHERE TO_CHAR(TO_TIMESTAMP(createtime, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24') = '$check_date $check_hour';"
    
    ksql -h $SRC_DB_HOST -p $SRC_DB_PORT -U $SRC_DB_USER -d $SRC_DB_NAME -W $SRC_DB_PASSWORD -t -A -c "$query"
}

# 按小时统计目标表数据量
count_dest_by_hour() {
    local check_date="$1"
    local check_hour="$2"
    local query="
    SELECT COUNT(*) 
    FROM $DEST_TABLE 
    WHERE TO_CHAR(TO_TIMESTAMP(createtime, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24') = '$check_date $check_hour';"
    
    ksql -h $DEST_DB_HOST -p $DEST_DB_PORT -U $DEST_DB_USER -d $DEST_DB_NAME -W $DEST_DB_PASSWORD -t -A -c "$query"
}

# 获取源表中指定小时的记录
get_src_records_by_hour() {
    local check_date="$1"
    local check_hour="$2"
    local query="
    SELECT dataid, certificateid, catalogid, catalogname, templateid, certificatetype, certificatetypecode, 
           issuedept, issuedeptcode, certificateareacode, certificateholder, certificateholdercode, 
           certificateholdertype, certificatenumber, issuedate, validbegindate, validenddate, 
           surfacedata, status, creator, createtime, operator, updatetime, filepath, 
           syncstatus, remarks, deptid, applynum, affairname, affairtype, servebusiness, 
           affairid, affairnum, qztype, zztype, drafturl, isview, sortname, col1, 
           verifydate, verification, creditcode, sealname
    FROM $SRC_TABLE 
    WHERE TO_CHAR(TO_TIMESTAMP(createtime, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24') = '$check_date $check_hour'
    ORDER BY createtime;"
    
    ksql -h $SRC_DB_HOST -p $SRC_DB_PORT -U $SRC_DB_USER -d $SRC_DB_NAME -W $SRC_DB_PASSWORD -t -A -F '|' -c "$query"
}

# 检查记录是否存在于目标表
check_record_exists() {
    local dataid="$1"
    local query="
    SELECT COUNT(*) 
    FROM $DEST_TABLE 
    WHERE dataid = '$dataid';"
    
    local count=$(ksql -h $DEST_DB_HOST -p $DEST_DB_PORT -U $DEST_DB_USER -d $DEST_DB_NAME -W $DEST_DB_PASSWORD -t -A -c "$query")
    
    if [ "$count" -gt 0 ]; then
        echo "1"
    else
        echo "0"
    fi
}

# 插入记录到目标表
insert_record() {
    local record="$1"
    
    # 解析记录字段
    IFS='|' read -r dataid certificateid catalogid catalogname templateid certificatetype certificatetypecode \
                   issuedept issuedeptcode certificateareacode certificateholder certificateholdercode \
                   certificateholdertype certificatenumber issuedate validbegindate validenddate \
                   surfacedata status creator createtime operator updatetime filepath \
                   syncstatus remarks deptid applynum affairname affairtype servebusiness \
                   affairid affairnum qztype zztype drafturl isview sortname col1 \
                   verifydate verification creditcode sealname <<< "$record"
    
    # 处理可能包含单引号的字段
    surfacedata=$(echo "$surfacedata" | sed "s/'/''/g")
    
    local query="
    INSERT INTO $DEST_TABLE (
        dataid, certificateid, catalogid, catalogname, templateid, certificatetype, certificatetypecode,
        issuedept, issuedeptcode, certificateareacode, certificateholder, certificateholdercode,
        certificateholdertype, certificatenumber, issuedate, validbegindate, validenddate,
        surfacedata, status, creator, createtime, operator, updatetime, filepath,
        syncstatus, remarks, deptid, applynum, affairname, affairtype, servebusiness,
        affairid, affairnum, qztype, zztype, drafturl, isview, sortname, col1,
        verifydate, verification, creditcode, sealname
    ) VALUES (
        '$dataid', '$certificateid', '$catalogid', '$catalogname', '$templateid', '$certificatetype', '$certificatetypecode',
        '$issuedept', '$issuedeptcode', '$certificateareacode', '$certificateholder', '$certificateholdercode',
        '$certificateholdertype', '$certificatenumber', '$issuedate', '$validbegindate', '$validenddate',
        '$surfacedata', '$status', '$creator', '$createtime', '$operator', '$updatetime', '$filepath',
        '$syncstatus', '$remarks', '$deptid', '$applynum', '$affairname', '$affairtype', '$servebusiness',
        '$affairid', '$affairnum', '$qztype', '$zztype', '$drafturl', '$isview', '$sortname', '$col1',
        '$verifydate', '$verification', '$creditcode', '$sealname'
    );"
    
    ksql -h $DEST_DB_HOST -p $DEST_DB_PORT -U $DEST_DB_USER -d $DEST_DB_NAME -W $DEST_DB_PASSWORD -c "$query" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "1"
    else
        echo "0"
    fi
}

# 主处理逻辑
log "开始按天比对数据..."

# 遍历日期范围
for check_date in $(generate_date_sequence "$START_DATE" "$END_DATE"); do
    src_count=$(count_src_by_day "$check_date")
    dest_count=$(count_dest_by_day "$check_date")
    
    log "日期: $check_date, 源表记录数: $src_count, 目标表记录数: $dest_count"
    
    # 如果数量不一致，按小时比对
    if [ "$src_count" != "$dest_count" ]; then
        log "发现数据不一致，开始按小时比对..."
        
        # 遍历24小时
        for hour in {00..23}; do
            src_hour_count=$(count_src_by_hour "$check_date" "$hour")
            dest_hour_count=$(count_dest_by_hour "$check_date" "$hour")
            
            log "日期: $check_date, 小时: $hour, 源表记录数: $src_hour_count, 目标表记录数: $dest_hour_count"
            
            # 如果小时数据不一致，查找缺失记录
            if [ "$src_hour_count" != "$dest_hour_count" ]; then
                log "发现小时 $hour 数据不一致，开始查找缺失记录..."
                
                # 获取源表中该小时的所有记录
                src_records=$(get_src_records_by_hour "$check_date" "$hour")
                
                # 批量处理记录，每批100条
                batch_size=100
                record_count=0
                missing_count=0
                sync_success=0
                sync_fail=0
                
                echo "$src_records" | while IFS= read -r record; do
                    if [ -z "$record" ]; then
                        continue
                    fi
                    
                    record_count=$((record_count + 1))
                    
                    # 提取dataid
                    dataid=$(echo "$record" | cut -d'|' -f1)
                    
                    # 检查记录是否存在于目标表
                    exists=$(check_record_exists "$dataid")
                    
                    if [ "$exists" -eq 0 ]; then
                        missing_count=$((missing_count + 1))
                        log "发现缺失记录: $dataid"
                        
                        # 插入记录到目标表
                        insert_result=$(insert_record "$record")
                        
                        if [ "$insert_result" -eq 1 ]; then
                            sync_success=$((sync_success + 1))
                            log "成功同步记录: $dataid"
                        else
                            sync_fail=$((sync_fail + 1))
                            log "同步记录失败: $dataid"
                        fi
                    fi
                    
                    # 每处理100条记录输出一次进度
                    if [ $((record_count % batch_size)) -eq 0 ]; then
                        log "已处理 $record_count 条记录，发现缺失 $missing_count 条，同步成功 $sync_success 条，同步失败 $sync_fail 条"
                    fi
                done
                
                log "小时 $hour 处理完成，共处理 $record_count 条记录，发现缺失 $missing_count 条，同步成功 $sync_success 条，同步失败 $sync_fail 条"
            fi
        done
    fi
done

log "数据同步比对完成"
exit 0 