2025-07-08 14:48:35.141 [main] INFO  com.example.certificate.CertificateEtlApplication - Starting CertificateEtlApplication using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 25712 (E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\classes started by deLl in E:\project\IdeaProjects\dzzz_etl)
2025-07-08 14:48:35.149 [main] INFO  com.example.certificate.CertificateEtlApplication - The following 1 profile is active: "dev"
2025-07-08 14:48:36.194 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-08 14:48:36.203 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-08 14:48:36.204 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 14:48:36.204 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-08 14:48:36.307 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-08 14:48:36.308 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1118 ms
2025-07-08 14:48:36.569 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-07-08 14:48:37.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-07-08 14:48:37.146 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 14:48:37.161 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-07-08 14:48:37.161 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 14:48:37.227 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:48:37.227 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:48:37.232 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:48:37.499 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-07-08 14:48:37.570 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-07-08 14:48:37.596 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 14:48:40.537 [main] ERROR com.zaxxer.hikari.pool.HikariPool - StandardHikariCP - Exception during pool initialization.
org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:646)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:180)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy67.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy71.getAllCertTypeInfo(Unknown Source)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.initCache(CertificateEtlServiceImpl.java:94)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.executeEtlTask(CertificateEtlServiceImpl.java:140)
	at com.example.certificate.service.CertificateService.init(CertificateService.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.example.certificate.CertificateEtlApplication.main(CertificateEtlApplication.java:19)
2025-07-08 14:48:40.546 [main] ERROR com.example.certificate.service.CertificateService - 证照数据ETL测试失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
### The error may exist in com/example/certificate/mapper/standard/CertTypeDirectoryMapper.java (best guess)
### The error may involve com.example.certificate.mapper.standard.CertTypeDirectoryMapper.getAllCertTypeInfo
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy67.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy71.getAllCertTypeInfo(Unknown Source)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.initCache(CertificateEtlServiceImpl.java:94)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.executeEtlTask(CertificateEtlServiceImpl.java:140)
	at com.example.certificate.service.CertificateService.init(CertificateService.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.example.certificate.CertificateEtlApplication.main(CertificateEtlApplication.java:19)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
### The error may exist in com/example/certificate/mapper/standard/CertTypeDirectoryMapper.java (best guess)
### The error may involve com.example.certificate.mapper.standard.CertTypeDirectoryMapper.getAllCertTypeInfo
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 35 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 42 common frames omitted
Caused by: org.postgresql.util.PSQLException: FATAL: �Բ���, �Ѿ���̫��Ŀͻ�
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:646)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:180)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 52 common frames omitted
2025-07-08 14:48:40.956 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-08 14:48:40.975 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-08 14:48:40.989 [main] INFO  com.example.certificate.CertificateEtlApplication - Started CertificateEtlApplication in 6.286 seconds (JVM running for 7.857)
2025-07-08 14:50:15.724 [main] INFO  com.example.certificate.CertificateEtlApplication - Starting CertificateEtlApplication using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 248 (E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\classes started by deLl in E:\project\IdeaProjects\dzzz_etl)
2025-07-08 14:50:15.728 [main] INFO  com.example.certificate.CertificateEtlApplication - The following 1 profile is active: "dev"
2025-07-08 14:50:16.878 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-08 14:50:16.886 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-08 14:50:16.887 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 14:50:16.887 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-08 14:50:17.001 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-08 14:50:17.001 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1216 ms
2025-07-08 14:50:17.310 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-07-08 14:50:17.873 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-07-08 14:50:17.873 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 14:50:17.915 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-07-08 14:50:17.916 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 14:50:18.070 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:50:18.071 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:50:18.081 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:50:18.463 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-07-08 14:50:18.541 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-07-08 14:50:18.558 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 14:50:20.587 [main] ERROR com.zaxxer.hikari.pool.HikariPool - StandardHikariCP - Exception during pool initialization.
org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:646)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:180)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy67.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy71.getAllCertTypeInfo(Unknown Source)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.initCache(CertificateEtlServiceImpl.java:94)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.executeEtlTask(CertificateEtlServiceImpl.java:140)
	at com.example.certificate.service.CertificateService.init(CertificateService.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.example.certificate.CertificateEtlApplication.main(CertificateEtlApplication.java:19)
2025-07-08 14:50:20.594 [main] ERROR com.example.certificate.service.CertificateService - 证照数据ETL测试失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
### The error may exist in com/example/certificate/mapper/standard/CertTypeDirectoryMapper.java (best guess)
### The error may involve com.example.certificate.mapper.standard.CertTypeDirectoryMapper.getAllCertTypeInfo
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy67.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy71.getAllCertTypeInfo(Unknown Source)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.initCache(CertificateEtlServiceImpl.java:94)
	at com.example.certificate.service.impl.CertificateEtlServiceImpl.executeEtlTask(CertificateEtlServiceImpl.java:140)
	at com.example.certificate.service.CertificateService.init(CertificateService.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.example.certificate.CertificateEtlApplication.main(CertificateEtlApplication.java:19)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
### The error may exist in com/example/certificate/mapper/standard/CertTypeDirectoryMapper.java (best guess)
### The error may involve com.example.certificate.mapper.standard.CertTypeDirectoryMapper.getAllCertTypeInfo
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:153)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 35 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:83)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	... 42 common frames omitted
Caused by: org.postgresql.util.PSQLException: FATAL: ���ݿ�ϵͳ������
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:646)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:180)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	... 52 common frames omitted
2025-07-08 14:50:21.269 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-08 14:50:21.302 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-08 14:50:21.343 [main] INFO  com.example.certificate.CertificateEtlApplication - Started CertificateEtlApplication in 6.344 seconds (JVM running for 7.517)
2025-07-08 14:51:47.900 [main] INFO  com.example.certificate.CertificateEtlApplication - Starting CertificateEtlApplication using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 18932 (E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\classes started by deLl in E:\project\IdeaProjects\dzzz_etl)
2025-07-08 14:51:47.904 [main] INFO  com.example.certificate.CertificateEtlApplication - The following 1 profile is active: "dev"
2025-07-08 14:51:48.969 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-08 14:51:48.979 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-08 14:51:48.980 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 14:51:48.980 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-08 14:51:49.119 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-08 14:51:49.119 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1171 ms
2025-07-08 14:51:49.503 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-07-08 14:51:50.056 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-07-08 14:51:50.057 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 14:51:50.072 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-07-08 14:51:50.073 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 14:51:50.143 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:51:50.143 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:51:50.150 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 14:51:50.444 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-07-08 14:51:50.517 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-07-08 14:51:50.545 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 14:51:51.664 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-08 14:51:51.742 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-07-08 14:51:51.782 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-07-08 14:51:51.783 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-07-08 14:51:52.244 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-07-08 14:51:52.262 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-07-08 14:51:52.262 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-07-08 14:51:52.289 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-07-08 14:51:52.289 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-07-08 14:51:52.289 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-07-08 14:51:52.289 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 14:51:52.289 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 14:51:52.371 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 原来的dataId变量: b0799a5b14c419b3c71cab9ff608abc091
2025-07-08 14:51:52.371 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 通过certificateId=11221060ABC901找到已存在记录，使用dataId=b0799a5b14c419b3c71cab9ff608abc091
2025-07-08 14:51:52.432 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 已清理目标表中的数据, dataId: b0799a5b14c419b3c71cab9ff608abc091, certificateType: 海船船员培训合格证承认签证
2025-07-08 14:51:52.601 [main] INFO  com.zaxxer.hikari.HikariDataSource - AggregateHikariCP - Starting...
2025-07-08 14:51:53.590 [main] INFO  com.zaxxer.hikari.HikariDataSource - AggregateHikariCP - Start completed.
2025-07-08 14:51:53.719 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 重处理数据完成 - 总数: 1, 成功: 1, 失败: 0; , 耗时: 1429毫秒
2025-07-08 14:51:53.719 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 重处理数据完成 - 总数: 1, 成功: 1, 失败: 0; 
2025-07-08 14:51:54.232 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-08 14:51:54.258 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-08 14:51:54.272 [main] INFO  com.example.certificate.CertificateEtlApplication - Started CertificateEtlApplication in 7.02 seconds (JVM running for 8.001)
2025-07-08 15:00:00.018 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 开始执行定时任务...
2025-07-08 15:00:00.018 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:00:00.019 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:00:00.097 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 78毫秒
2025-07-08 15:00:00.097 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 定时任务执行结果: 
2025-07-08 15:00:10.015 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 开始执行定时任务...
2025-07-08 15:00:10.016 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:00:10.016 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:00:10.106 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 90毫秒
2025-07-08 15:00:10.106 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 定时任务执行结果: 
2025-07-08 15:00:20.005 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 开始执行定时任务...
2025-07-08 15:00:20.005 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:00:20.005 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:00:20.086 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 81毫秒
2025-07-08 15:00:20.086 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 定时任务执行结果: 
2025-07-08 15:00:30.014 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 开始执行定时任务...
2025-07-08 15:00:30.014 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:00:30.014 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:00:30.087 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 73毫秒
2025-07-08 15:00:30.088 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 定时任务执行结果: 
2025-07-08 15:00:40.011 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 开始执行定时任务...
2025-07-08 15:00:40.011 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:00:40.011 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:00:40.114 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 103毫秒
2025-07-08 15:00:40.114 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 定时任务执行结果: 
2025-07-08 15:00:50.009 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 开始执行定时任务...
2025-07-08 15:00:50.009 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:00:50.010 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:00:50.086 [scheduling-1] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 76毫秒
2025-07-08 15:00:50.086 [scheduling-1] INFO  com.example.certificate.task.CertificateEtlTask - 定时任务执行结果: 
2025-07-08 15:05:54.619 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-08 15:05:54.621 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-07-08 15:05:54.622 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - AggregateHikariCP - Shutdown initiated...
2025-07-08 15:05:54.623 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - AggregateHikariCP - Shutdown completed.
